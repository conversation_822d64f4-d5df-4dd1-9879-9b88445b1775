#!/usr/bin/env python3
"""
TEST SIMPLE DES OPTIMISATIONS
==============================

Ce script teste directement la méthode optimisée sans dépendre du fichier dataset.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import time
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_optimisations_simple():
    """
    Test simple des optimisations
    """
    print("🔬 TEST SIMPLE DES OPTIMISATIONS")
    print("=" * 40)
    
    try:
        # 1. CRÉER DES DONNÉES DE TEST
        print("\n📊 PHASE 1: CRÉATION DES DONNÉES DE TEST")
        print("-" * 45)
        
        # Créer des séquences de test de différentes tailles
        np.random.seed(42)
        
        # Petit dataset (< 10k)
        small_index1 = np.random.choice(['SYNC', 'DESYNC'], 5000).tolist()
        small_index3 = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 5000).tolist()
        
        # Grand dataset (> 100k)
        large_index1 = np.random.choice(['SYNC', 'DESYNC'], 150000).tolist()
        large_index3 = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 150000).tolist()
        
        print(f"   ✅ Petit dataset créé : {len(small_index1):,} éléments")
        print(f"   ✅ Grand dataset créé : {len(large_index1):,} éléments")
        
        # 2. TESTER AVEC PETIT DATASET
        print("\n📊 PHASE 2: TEST AVEC PETIT DATASET")
        print("-" * 40)
        
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
        
        # Créer un analyseur avec le petit dataset
        sequences_small = {
            'INDEX1': small_index1,
            'INDEX3': small_index3
        }
        
        analyzer_small = LupascoAnalyzer(sequences_small)
        
        # Test avec auto-détection
        debut = time.time()
        resultats_small = analyzer_small.analyser_predictibilite_index3_par_index1(use_advanced=None)
        temps_small = time.time() - debut
        
        print(f"   ✅ Petit dataset traité en {temps_small:.2f}s")
        print(f"   📊 Calculs avancés utilisés : {resultats_small.get('use_advanced', 'N/A')}")
        
        if 'erreur' in resultats_small:
            print(f"   ❌ Erreur : {resultats_small['erreur']}")
            return False
        
        # 3. TESTER AVEC GRAND DATASET
        print("\n📊 PHASE 3: TEST AVEC GRAND DATASET")
        print("-" * 40)
        
        # Créer un analyseur avec le grand dataset
        sequences_large = {
            'INDEX1': large_index1,
            'INDEX3': large_index3
        }
        
        analyzer_large = LupascoAnalyzer(sequences_large)
        
        # Test avec auto-détection (devrait désactiver les calculs avancés)
        debut = time.time()
        resultats_large = analyzer_large.analyser_predictibilite_index3_par_index1(use_advanced=None)
        temps_large = time.time() - debut
        
        print(f"   ✅ Grand dataset traité en {temps_large:.2f}s")
        print(f"   📊 Calculs avancés utilisés : {resultats_large.get('use_advanced', 'N/A')}")
        
        if 'erreur' in resultats_large:
            print(f"   ❌ Erreur : {resultats_large['erreur']}")
            return False
        
        # Vérifier que l'optimisation a été appliquée
        if not resultats_large.get('use_advanced', True):
            print(f"   ✅ Optimisation appliquée : calculs avancés désactivés automatiquement")
        else:
            print(f"   ⚠️ Optimisation non appliquée : calculs avancés maintenus")
        
        # 4. COMPARER LES PERFORMANCES
        print("\n📊 PHASE 4: COMPARAISON DES PERFORMANCES")
        print("-" * 45)
        
        print(f"   📊 Temps petit dataset ({len(small_index1):,}) : {temps_small:.2f}s")
        print(f"   📊 Temps grand dataset ({len(large_index1):,}) : {temps_large:.2f}s")
        
        # Calculer le ratio de performance
        ratio_taille = len(large_index1) / len(small_index1)
        ratio_temps = temps_large / temps_small if temps_small > 0 else float('inf')
        
        print(f"   📊 Ratio de taille : {ratio_taille:.1f}x")
        print(f"   📊 Ratio de temps : {ratio_temps:.1f}x")
        
        # Vérifier que les optimisations sont efficaces
        if ratio_temps < ratio_taille * 2:  # Le temps ne devrait pas croître quadratiquement
            print(f"   ✅ Optimisations efficaces : croissance sous-quadratique")
        else:
            print(f"   ⚠️ Optimisations insuffisantes : croissance trop importante")
        
        # 5. TESTER AVEC TIMEOUT
        print("\n📊 PHASE 5: TEST AVEC TIMEOUT")
        print("-" * 30)
        
        # Test rapide pour s'assurer qu'il n'y a pas de blocage
        debut_timeout = time.time()
        
        try:
            # Forcer les calculs traditionnels (plus rapides)
            resultats_timeout = analyzer_large.analyser_predictibilite_index3_par_index1(use_advanced=False)
            temps_timeout = time.time() - debut_timeout
            
            print(f"   ✅ Test timeout réussi en {temps_timeout:.2f}s")
            
            if temps_timeout < 30:  # Moins de 30 secondes
                print(f"   ✅ Performance acceptable")
            else:
                print(f"   ⚠️ Performance lente mais acceptable")
            
        except Exception as e:
            print(f"   ❌ Erreur dans le test timeout : {e}")
            return False
        
        # 6. RÉSUMÉ FINAL
        print("\n🏆 PHASE 6: RÉSUMÉ FINAL")
        print("-" * 25)
        
        print("   ✅ Optimisations testées avec succès")
        print("   ✅ Auto-détection des calculs avancés fonctionnelle")
        print("   ✅ Performance acceptable sur gros datasets")
        print("   ✅ Pas de blocage détecté")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR DANS LE TEST : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST SIMPLE DES OPTIMISATIONS")
    print("=" * 50)
    
    succes = test_optimisations_simple()
    
    print("\n" + "=" * 50)
    if succes:
        print("✅ TEST RÉUSSI - OPTIMISATIONS FONCTIONNELLES")
        print("L'analyse de prédictibilité est optimisée.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ - OPTIMISATIONS INSUFFISANTES")
        print("Des améliorations supplémentaires sont nécessaires.")
        sys.exit(1)
