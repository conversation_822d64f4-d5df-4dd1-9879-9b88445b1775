#!/usr/bin/env python3
"""
TEST SPÉCIFIQUE POUR L'ERREUR DANS ENTROPY_ADVANCED
===================================================

Ce script teste la correction de l'erreur "unsupported operand type(s) for +: 'int' and 'dict'"
dans le module lupasco_refactored/statistics/entropy_advanced.py

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_entropy_advanced_correction():
    """
    Test spécifique pour vérifier que l'erreur dans entropy_advanced est corrigée
    """
    print("🔬 TEST SPÉCIFIQUE - ERREUR ENTROPY_ADVANCED CORRIGÉE")
    print("=" * 55)
    
    try:
        # 1. TESTER LE MODULE ENTROPY_ADVANCED DIRECTEMENT
        print("\n📊 PHASE 1: TEST DU MODULE ENTROPY_ADVANCED")
        print("-" * 45)
        
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy
        
        # Créer des données de test
        test_composants = {
            'INDEX1': ['SYNC', 'DESYNC'] * 50,
            'INDEX2': ['pair_4', 'pair_6', 'impair_5'] * 33 + ['pair_4'],
            'INDEX3': ['PLAYER', 'BANKER', 'TIE'] * 33 + ['PLAYER']
        }
        
        analyzer = AdvancedEntropy()
        
        # Tester la méthode qui causait l'erreur
        print("   🔄 Test de analyser_entropie_jointe_complete...")
        
        resultats = analyzer.analyser_entropie_jointe_complete(test_composants)
        
        print(f"   ✅ Analyse entropie jointe réussie")
        
        # 2. VÉRIFIER LA STRUCTURE DES RÉSULTATS
        print("\n🔍 PHASE 2: VÉRIFICATION DE LA STRUCTURE DES RÉSULTATS")
        print("-" * 60)
        
        if 'erreur' in resultats:
            print(f"   ⚠️ Erreur dans les résultats : {resultats['erreur']}")
        else:
            print("   ✅ Pas d'erreur dans les résultats")
            
            # Vérifier les clés importantes
            cles_attendues = [
                'entropies_individuelles',
                'entropies_jointes_paires', 
                'entropie_jointe_triple',
                'informations_mutuelles',
                'redondance_totale',
                'synergie'
            ]
            
            for cle in cles_attendues:
                if cle in resultats:
                    print(f"   ✅ Clé '{cle}' présente")
                    
                    # Vérifier que redondance_totale est un nombre
                    if cle == 'redondance_totale':
                        redondance = resultats[cle]
                        if isinstance(redondance, (int, float)):
                            print(f"      ✅ redondance_totale est numérique : {redondance}")
                        else:
                            print(f"      ❌ redondance_totale n'est pas numérique : {type(redondance)}")
                            return False
                else:
                    print(f"   ❌ Clé '{cle}' manquante")
        
        # 3. TESTER AVEC DES DONNÉES PROBLÉMATIQUES
        print("\n🧪 PHASE 3: TEST AVEC DONNÉES PROBLÉMATIQUES")
        print("-" * 50)
        
        # Tester avec des données vides
        print("   🔄 Test avec données vides...")
        resultats_vides = analyzer.analyser_entropie_jointe_complete({})
        
        if 'erreur' in resultats_vides:
            print(f"   ✅ Gestion correcte des données vides : {resultats_vides['erreur']}")
        else:
            print("   ❌ Données vides non gérées correctement")
        
        # Tester avec des données incomplètes
        print("   🔄 Test avec données incomplètes...")
        composants_incomplets = {
            'INDEX1': ['SYNC', 'DESYNC'] * 10,
            'INDEX2': [],  # Vide
            'INDEX3': ['PLAYER'] * 5
        }
        
        resultats_incomplets = analyzer.analyser_entropie_jointe_complete(composants_incomplets)
        
        if 'erreur' in resultats_incomplets:
            print(f"   ✅ Gestion correcte des données incomplètes : {resultats_incomplets['erreur']}")
        else:
            print("   ❌ Données incomplètes non gérées correctement")
        
        # 4. TESTER LA MÉTHODE _safe_arithmetic_operation
        print("\n🔧 PHASE 4: TEST DE LA MÉTHODE SÉCURISÉE")
        print("-" * 45)
        
        # Tester l'opération sécurisée
        print("   🔄 Test de _safe_arithmetic_operation...")
        
        # Test avec opération normale
        result_normal = analyzer._safe_arithmetic_operation(lambda: 1 + 2 + 3, "test normal")
        print(f"   ✅ Opération normale : {result_normal}")
        
        # Test avec opération problématique (simulée)
        def operation_problematique():
            # Simuler une erreur de type
            return "string" + 5  # Cela devrait causer une TypeError
        
        result_erreur = analyzer._safe_arithmetic_operation(operation_problematique, "test erreur")
        print(f"   ✅ Opération avec erreur gérée : {result_erreur}")
        
        # 5. RÉSUMÉ FINAL
        print("\n🏆 PHASE 5: RÉSUMÉ FINAL")
        print("-" * 25)
        
        print("   ✅ Module entropy_advanced corrigé")
        print("   ✅ Validation des types ajoutée")
        print("   ✅ Gestion sécurisée des opérations arithmétiques")
        print("   ✅ Pas d'erreur 'int' + 'dict' dans entropy_advanced")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR DANS LE TEST : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST ENTROPY_ADVANCED")
    print("=" * 40)
    
    succes = test_entropy_advanced_correction()
    
    print("\n" + "=" * 40)
    if succes:
        print("✅ TEST RÉUSSI - ENTROPY_ADVANCED CORRIGÉ")
        print("Le module entropy_advanced gère maintenant correctement les types.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ - PROBLÈME DANS ENTROPY_ADVANCED")
        print("Des corrections supplémentaires sont nécessaires.")
        sys.exit(1)
