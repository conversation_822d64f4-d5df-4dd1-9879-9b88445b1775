"""
UTILS - MATHEMATICAL FORMULAS
==============================

Module contenant les formules mathématiques pures
pour l'analyseur Lupasco refactorisé.

Formules d'entropie et d'information sans dépendances externes.

Version : 2.0
Auteur : Système Lupasco Refactorisé
Date : 2025-06-19
"""

import numpy as np
import math
from collections import defaultdict, Counter
from typing import List, Union, Tuple


def entropie_renyi(data: List[str], alpha: float) -> float:
    """
    Calcule l'entropie de Rényi d'ordre α
    
    Args:
        data: Séquence de données
        alpha: Paramètre d'ordre (α ≠ 1)
        
    Returns:
        float: Entropie de Rényi en bits
    """
    if alpha == 1:
        # Cas limite : entropie de Shannon
        from lupasco_refactored.utils.data_utils import calculer_entropie_shannon
        return calculer_entropie_shannon(data)
    
    # Calculer les probabilités
    unique, counts = np.unique(data, return_counts=True)
    probabilities = counts / len(data)
    
    if alpha == 0:
        # Entropie de Hartley : log du nombre d'états
        return math.log2(len(unique))
    
    if alpha == float('inf'):
        # Entropie min : -log de la probabilité maximale
        return -math.log2(np.max(probabilities))
    
    # Formule générale de Rényi
    sum_p_alpha = np.sum(probabilities ** alpha)
    
    if sum_p_alpha <= 0:
        return 0.0
    
    return (1 / (1 - alpha)) * math.log2(sum_p_alpha)


def entropie_conditionnelle(Y: List[str], X: List[str]) -> float:
    """
    Calcule l'entropie conditionnelle H(Y|X)
    
    Args:
        Y: Variable dépendante (ex: INDEX3)
        X: Variable conditionnelle (ex: INDEX1 ou INDEX2)
        
    Returns:
        float: Entropie conditionnelle en bits
    """
    # Construire les paires (x, y)
    if isinstance(X[0], (list, tuple)):
        # X est déjà une combinaison
        pairs = list(zip(X, Y))
    else:
        pairs = list(zip(X, Y))
    
    # Compter les occurrences
    joint_counts = defaultdict(int)
    x_counts = defaultdict(int)
    
    for x, y in pairs:
        if isinstance(x, (list, tuple)):
            x_key = tuple(x)
        else:
            x_key = x
        joint_counts[(x_key, y)] += 1
        x_counts[x_key] += 1
    
    total = len(pairs)
    h_conditional = 0.0
    
    # Calculer H(Y|X) = Σ p(x) × H(Y|X=x)
    for x_val, x_count in x_counts.items():
        p_x = x_count / total
        
        # Calculer H(Y|X=x)
        y_given_x_counts = defaultdict(int)
        for (x_key, y_val), count in joint_counts.items():
            if x_key == x_val:
                y_given_x_counts[y_val] = count
        
        h_y_given_x = 0.0
        for y_val, count in y_given_x_counts.items():
            p_y_given_x = count / x_count
            if p_y_given_x > 0:
                h_y_given_x -= p_y_given_x * math.log2(p_y_given_x)
        
        h_conditional += p_x * h_y_given_x
    
    return h_conditional


def entropie_jointe(X: List[str], Y: List[str]) -> float:
    """
    Calcule l'entropie jointe H(X,Y)
    
    Args:
        X, Y: Variables aléatoires
        
    Returns:
        float: Entropie jointe en bits
    """
    from lupasco_refactored.utils.data_utils import calculer_entropie_shannon
    
    # Créer les paires (x, y)
    pairs = list(zip(X, Y))
    return calculer_entropie_shannon(pairs)


def information_mutuelle(X: List[str], Y: List[str]) -> float:
    """
    Calcule l'information mutuelle I(X;Y) = H(X) + H(Y) - H(X,Y)
    
    Args:
        X, Y: Variables aléatoires
        
    Returns:
        float: Information mutuelle en bits
    """
    from lupasco_refactored.utils.data_utils import calculer_entropie_shannon
    
    h_x = calculer_entropie_shannon(X)
    h_y = calculer_entropie_shannon(Y)
    h_xy = entropie_jointe(X, Y)
    
    return h_x + h_y - h_xy


def entropie_croisee(p_reelle: List[float], p_theorique: List[float]) -> float:
    """
    Calcule l'entropie croisée H(p,q) = -Σ p(i) × log(q(i))
    
    Args:
        p_reelle: Distribution réelle observée
        p_theorique: Distribution théorique de référence
        
    Returns:
        float: Entropie croisée en bits
    """
    p_reelle = np.array(p_reelle)
    p_theorique = np.array(p_theorique)
    
    # Éviter log(0)
    p_theorique = np.maximum(p_theorique, 1e-10)
    
    h_croisee = 0.0
    for i in range(len(p_reelle)):
        if p_reelle[i] > 0:
            h_croisee -= p_reelle[i] * math.log2(p_theorique[i])
    
    return h_croisee


def kl_divergence(p: List[float], q: List[float]) -> float:
    """
    Calcule la divergence de Kullback-Leibler D_KL(P||Q)
    
    Args:
        p: Distribution de probabilité P
        q: Distribution de probabilité Q
        
    Returns:
        float: Divergence KL en bits
    """
    p = np.array(p)
    q = np.array(q)
    
    # Éviter division par 0 et log(0)
    q = np.maximum(q, 1e-10)
    
    divergence = 0.0
    for i in range(len(p)):
        if p[i] > 0:
            divergence += p[i] * math.log2(p[i] / q[i])
    
    return divergence


def entropie_conditionnelle_corrigee(y_data: List[str], x_data: List[str]) -> float:
    """
    Calcule l'entropie conditionnelle H(Y|X) avec correction de bugs
    Version corrigée pour éviter les erreurs de calcul
    
    Args:
        y_data: Variable dépendante
        x_data: Variable conditionnelle
        
    Returns:
        float: Entropie conditionnelle en bits
    """
    if not y_data or not x_data or len(y_data) != len(x_data):
        return 0.0
    
    # Calcul direct des occurrences par valeur de X
    x_counts = Counter(x_data)
    total = len(y_data)
    h_conditional = 0.0
    
    # Pour chaque valeur de X
    for x_val in x_counts:
        # Filtrer directement Y pour cette valeur de X
        y_given_x = [y_data[i] for i in range(len(y_data)) if x_data[i] == x_val]
        
        if y_given_x:
            p_x = len(y_given_x) / total
            y_counts = Counter(y_given_x)
            total_y_given_x = len(y_given_x)
            
            # Calculer H(Y|X=x_val)
            h_y_given_x = 0.0
            for y_val, count in y_counts.items():
                if count > 0:
                    p_y_given_x = count / total_y_given_x
                    h_y_given_x -= p_y_given_x * math.log2(p_y_given_x)
            
            h_conditional += p_x * h_y_given_x
    
    return h_conditional


def information_mutuelle_corrigee(x_data: List[str], y_data: List[str]) -> float:
    """
    Calcule l'information mutuelle I(X;Y) = H(Y) - H(Y|X) avec correction
    
    Args:
        x_data: Variable X
        y_data: Variable Y
        
    Returns:
        float: Information mutuelle en bits
    """
    from lupasco_refactored.utils.data_utils import calculer_entropie_shannon
    
    if not x_data or not y_data or len(x_data) != len(y_data):
        return 0.0
    
    h_y = calculer_entropie_shannon(y_data)
    h_y_given_x = entropie_conditionnelle_corrigee(y_data, x_data)
    
    # L'information mutuelle ne peut pas être négative
    mutual_info = h_y - h_y_given_x

    # Forcer à 0 seulement si très légèrement négatif (erreur numérique)
    if mutual_info < -1e-10:
        # Erreur significative - garder la valeur pour diagnostic
        pass
    elif mutual_info < 0:
        # Erreur numérique mineure - corriger à 0
        mutual_info = 0.0
    
    return mutual_info


def valider_formules_mathematiques() -> dict:
    """
    Valide toutes les formules mathématiques avec des cas de test
    
    Returns:
        dict: Résultats de validation
    """
    resultats = {
        'tests_passes': 0,
        'tests_totaux': 0,
        'details': {}
    }
    
    # Test 1: Entropie de Rényi
    try:
        data_test = ['A', 'A', 'B', 'B', 'C', 'C']
        h_renyi_0 = entropie_renyi(data_test, 0)  # Doit être log2(3) = 1.585
        h_renyi_1 = entropie_renyi(data_test, 1)  # Entropie de Shannon
        h_renyi_2 = entropie_renyi(data_test, 2)  # Entropie de collision
        
        resultats['details']['entropie_renyi'] = {
            'alpha_0': h_renyi_0,
            'alpha_1': h_renyi_1,
            'alpha_2': h_renyi_2,
            'valide': abs(h_renyi_0 - math.log2(3)) < 1e-6
        }
        if resultats['details']['entropie_renyi']['valide']:
            resultats['tests_passes'] += 1
        resultats['tests_totaux'] += 1
    except Exception as e:
        resultats['details']['entropie_renyi'] = {'erreur': str(e)}
        resultats['tests_totaux'] += 1
    
    # Test 2: Entropie conditionnelle
    try:
        X_test = ['A', 'A', 'B', 'B']
        Y_test = ['1', '2', '1', '2']
        h_cond = entropie_conditionnelle(Y_test, X_test)
        
        resultats['details']['entropie_conditionnelle'] = {
            'valeur': h_cond,
            'valide': 0 <= h_cond <= 1  # Doit être entre 0 et 1 pour ce cas
        }
        if resultats['details']['entropie_conditionnelle']['valide']:
            resultats['tests_passes'] += 1
        resultats['tests_totaux'] += 1
    except Exception as e:
        resultats['details']['entropie_conditionnelle'] = {'erreur': str(e)}
        resultats['tests_totaux'] += 1
    
    # Test 3: Information mutuelle
    try:
        X_test = ['A', 'A', 'B', 'B']
        Y_test = ['1', '1', '2', '2']  # Parfaitement corrélé
        mi = information_mutuelle(X_test, Y_test)
        
        resultats['details']['information_mutuelle'] = {
            'valeur': mi,
            'valide': mi >= 0  # Doit être positive
        }
        if resultats['details']['information_mutuelle']['valide']:
            resultats['tests_passes'] += 1
        resultats['tests_totaux'] += 1
    except Exception as e:
        resultats['details']['information_mutuelle'] = {'erreur': str(e)}
        resultats['tests_totaux'] += 1
    
    # Test 4: Divergence KL
    try:
        p = [0.5, 0.3, 0.2]
        q = [0.4, 0.4, 0.2]
        div_kl = kl_divergence(p, q)
        
        resultats['details']['kl_divergence'] = {
            'valeur': div_kl,
            'valide': div_kl >= 0  # Doit être positive
        }
        if resultats['details']['kl_divergence']['valide']:
            resultats['tests_passes'] += 1
        resultats['tests_totaux'] += 1
    except Exception as e:
        resultats['details']['kl_divergence'] = {'erreur': str(e)}
        resultats['tests_totaux'] += 1
    
    # Calcul du taux de réussite
    resultats['taux_reussite'] = resultats['tests_passes'] / resultats['tests_totaux'] if resultats['tests_totaux'] > 0 else 0
    resultats['validation_complete'] = resultats['tests_passes'] == resultats['tests_totaux']
    
    return resultats


# Fonctions d'export pour compatibilité
__all__ = [
    'entropie_renyi',
    'entropie_conditionnelle',
    'entropie_jointe',
    'information_mutuelle',
    'entropie_croisee',
    'kl_divergence',
    'entropie_conditionnelle_corrigee',
    'information_mutuelle_corrigee',
    'valider_formules_mathematiques'
]
