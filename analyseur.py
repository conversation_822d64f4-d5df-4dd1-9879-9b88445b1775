#!/usr/bin/env python3
"""
ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO - VERSION STRUCTURÉE
================================================================

Version structurée avec organisation claire des méthodes par catégories.

STRUCTURE DU FICHIER :
=====================

📋 CLASSE PRINCIPALE : AnalyseurSequencesLupasco
├── 🔧 Méthodes de base (chargement, extraction)
├── 🎯 MÉTHODES D'ANALYSE INDEX5 (18 combinaisons)
│   ├── analyser_index5_avec_formules_exactes()
│   ├── 🔧 Méthodes de support INDEX5 - Transitions
│   └── 🔧 Méthodes de support INDEX5 - Patterns temporels
├── 🎯 MÉTHODES D'ANALYSE INDEX2_INDEX3 (9 combinaisons)
│   ├── analyser_index2_index3_avec_formules_exactes()
│   ├── 🔧 Méthodes de support INDEX2_INDEX3 - Transitions
│   └── 🔧 Méthodes de support INDEX2_INDEX3 - Patterns temporels
├── 🎯 MÉTHODES D'ANALYSE INDEX1_INDEX3 (6 combinaisons)
│   ├── analyser_index1_index3_avec_formules_exactes()
│   ├── 🔧 Méthodes de support INDEX1_INDEX3 - Transitions
│   └── 🔧 Méthodes de support INDEX1_INDEX3 - Patterns temporels
└── 🎯 MÉTHODES GÉNÉRALES ET UTILITAIRES
    ├── analyser_toutes_sequences()
    ├── generer_rapport()
    └── Méthodes communes

INDICES ANALYSÉS :
=================
• INDEX5 : SYNC/DESYNC + pair_4/pair_6/impair_5 + BANKER/PLAYER/TIE (18 combinaisons)
• INDEX2_INDEX3 : pair_4/pair_6/impair_5 + BANKER/PLAYER/TIE (9 combinaisons)
• INDEX1_INDEX3 : SYNC/DESYNC + BANKER/PLAYER/TIE (6 combinaisons)

Source : ../analyseur_sequences_lupasco.py
Date: 2025-06-19
"""

import json
import ijson
import numpy as np
import os
import gc
from collections import Counter, defaultdict
from datetime import datetime
from scipy import stats
from scipy.stats import geom, kstest, chi2_contingency
import math
import sys
import pandas as pd

# Importer les formules mathématiques exactes
sys.path.append('..')
from formules_mathematiques_exactes import (
    gini_coefficient, shannon_entropy_from_data,
    autocorrelation_function, coefficient_of_variation,
    runs_test, lupasco_entropy_analysis,
    z_score, detect_anomalies, lupasco_statistical_summary,
    validate_formulas
)

# Vérification ijson (requis pour gros fichiers)
try:
    import ijson
    HAS_IJSON = True
except ImportError:
    HAS_IJSON = False

# Vérification matplotlib (optionnel)
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False


class AnalyseurSequencesLupasco:
    """
    Analyseur statistique des séquences Lupasco - Version simplifiée
    """

    def __init__(self, fichier_json: str):
        """
        Initialise l'analyseur avec le fichier JSON

        Args:
            fichier_json: Chemin vers le fichier JSON du dataset Lupasco
        """
        self.fichier_json = fichier_json
        self.donnees = None
        self.sequences = {}
        self.resultats = {}
        self.nb_parties_total = 0

        print("🔬 ANALYSEUR STATISTIQUE DES SÉQUENCES LUPASCO")
        print("=" * 60)
        print(f"📂 Fichier à analyser : {fichier_json}")

    def charger_donnees(self):
        """
        Charge et extrait les données du fichier JSON volumineux (7GB) avec streaming
        WRAPPER vers lupasco_refactored.core.data_loader.DataLoader
        """
        from lupasco_refactored.core.data_loader import DataLoader

        print("\n📊 Chargement des données...")

        try:
            # Utiliser le nouveau DataLoader
            loader = DataLoader(self.fichier_json)

            # Déterminer si on force le streaming (fichiers > 1 GB)
            taille_fichier = os.path.getsize(self.fichier_json)
            taille_gb = taille_fichier / (1024 * 1024 * 1024)
            force_streaming = taille_gb >= 1

            resultats = loader.charger_donnees(force_streaming)

            # Adapter les résultats pour la compatibilité
            self.sequences = resultats['sequences']
            self.nb_parties_total = resultats['nb_parties_total']

            # Conserver les données si disponibles (pour compatibilité)
            if 'donnees' in resultats:
                self.donnees = resultats['donnees']

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise

    def _charger_avec_streaming(self):
        """
        Charge le fichier JSON avec streaming ijson (pour fichiers 7GB)
        WRAPPER vers lupasco_refactored.core.data_loader.DataLoader._charger_avec_streaming
        """
        from lupasco_refactored.core.data_loader import DataLoader

        loader = DataLoader(self.fichier_json)
        resultats = loader._charger_avec_streaming()

        # Adapter les résultats pour la compatibilité
        self.sequences = resultats['sequences']
        self.nb_parties_total = resultats['nb_parties_total']

    def _charger_standard(self):
        """
        Chargement standard pour fichiers plus petits
        WRAPPER vers lupasco_refactored.core.data_loader.DataLoader._charger_standard
        """
        from lupasco_refactored.core.data_loader import DataLoader

        loader = DataLoader(self.fichier_json)
        resultats = loader._charger_standard()

        # Adapter les résultats pour la compatibilité
        self.sequences = resultats['sequences']
        self.nb_parties_total = resultats['nb_parties_total']

        # Conserver les données si disponibles (pour compatibilité)
        if 'donnees' in resultats:
            self.donnees = resultats['donnees']

    def _extraire_sequences(self):
        """
        Extrait les séquences de tous les index ET leurs combinaisons pour toutes les parties
        WRAPPER vers lupasco_refactored.core.data_loader.DataLoader._extraire_sequences
        """
        from lupasco_refactored.core.data_loader import DataLoader

        loader = DataLoader(self.fichier_json)
        loader.donnees = self.donnees  # Passer les données déjà chargées
        total_mains = loader._extraire_sequences()

        # Adapter les résultats pour la compatibilité
        self.sequences = loader.sequences

        return total_mains

    def _nettoyer_marqueurs(self, sequence):
        """
        Supprime les marqueurs __FIN_PARTIE__ d'une séquence
        WRAPPER vers lupasco_refactored.utils.data_utils.nettoyer_marqueurs
        """
        from lupasco_refactored.utils.data_utils import nettoyer_marqueurs
        return nettoyer_marqueurs(sequence)

    def _extraire_composants_index5(self):
        """
        Extrait INDEX1, INDEX2, INDEX3 depuis self.sequences['INDEX5']
        WRAPPER vers lupasco_refactored.core.sequence_extractor.SequenceExtractor.extraire_composants_index5
        Format: "SYNC_pair_4_BANKER" → ["SYNC", "pair_4", "BANKER"]
        """
        from lupasco_refactored.core.sequence_extractor import SequenceExtractor

        extractor = SequenceExtractor(self.sequences)
        return extractor.extraire_composants_index5()

    def _extraire_composants_index2_index3(self):
        """
        Extrait INDEX2, INDEX3 depuis self.sequences['INDEX2_INDEX3']
        WRAPPER vers lupasco_refactored.core.sequence_extractor.SequenceExtractor.extraire_composants_index2_index3
        Format: "pair_4_BANKER" → ["pair_4", "BANKER"]
        """
        from lupasco_refactored.core.sequence_extractor import SequenceExtractor

        extractor = SequenceExtractor(self.sequences)
        return extractor.extraire_composants_index2_index3()

    def _extraire_composants_index1_index3(self):
        """
        Extrait INDEX1, INDEX3 depuis self.sequences['INDEX1_INDEX3']
        WRAPPER vers lupasco_refactored.core.sequence_extractor.SequenceExtractor.extraire_composants_index1_index3
        Format: "SYNC_BANKER" → ["SYNC", "BANKER"]
        """
        from lupasco_refactored.core.sequence_extractor import SequenceExtractor

        extractor = SequenceExtractor(self.sequences)
        return extractor.extraire_composants_index1_index3()

    def analyser_runs(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse les runs (séquences consécutives) d'une séquence
        WRAPPER vers lupasco_refactored.statistics.basic_stats.BasicStatistics.analyser_runs
        Respecte l'indépendance des parties

        Args:
            sequence: Liste des valeurs de la séquence
            nom_sequence: Nom de la séquence pour l'affichage

        Returns:
            dict: Résultats de l'analyse des runs
        """
        from lupasco_refactored.statistics.basic_stats import BasicStatistics

        stats = BasicStatistics()
        return stats.analyser_runs(sequence, nom_sequence)

    def _analyser_runs_valeur(self, longueurs: list, valeur: str, n_total: int, n_valeur: int) -> dict:
        """
        Analyse les runs pour une valeur spécifique
        WRAPPER vers lupasco_refactored.statistics.basic_stats.BasicStatistics.analyser_runs_valeur
        """
        from lupasco_refactored.statistics.basic_stats import BasicStatistics

        stats = BasicStatistics()
        return stats.analyser_runs_valeur(longueurs, valeur, n_total, n_valeur)

    def _analyser_runs_global(self, runs: list, sequence: list) -> dict:
        """
        Analyse globale des runs
        WRAPPER vers lupasco_refactored.statistics.basic_stats.BasicStatistics.analyser_runs_global
        """
        from lupasco_refactored.statistics.basic_stats import BasicStatistics

        stats = BasicStatistics()
        return stats.analyser_runs_global(runs, sequence)

    def calculer_autocorrelation(self, sequence: list, max_lag: int = 20) -> dict:
        """
        Calcule l'autocorrélation de la séquence
        WRAPPER vers lupasco_refactored.statistics.basic_stats.BasicStatistics.calculer_autocorrelation

        Args:
            sequence: Séquence à analyser
            max_lag: Nombre maximum de lags à calculer

        Returns:
            dict: Coefficients d'autocorrélation
        """
        from lupasco_refactored.statistics.basic_stats import BasicStatistics

        stats = BasicStatistics()
        return stats.calculer_autocorrelation(sequence, max_lag)

    def calculer_entropie_shannon(self, sequence: list) -> float:
        """
        Calcule l'entropie de Shannon de la séquence
        WRAPPER vers lupasco_refactored.utils.data_utils.calculer_entropie_shannon

        Args:
            sequence: Séquence à analyser

        Returns:
            float: Entropie de Shannon
        """
        from lupasco_refactored.utils.data_utils import calculer_entropie_shannon
        return calculer_entropie_shannon(sequence)



    def _detecter_frontieres_parties_precises(self) -> dict:
        """
        🔧 SOLUTION : Détecte les frontières exactes entre parties basées sur les compteurs de mains

        🎯 CORRECTION : Détecte la PREMIÈRE occurrence de main_number = 1 (début de partie)
                       et la DERNIÈRE occurrence de la dernière main (fin de partie)

        Returns:
            dict: {
                'frontieres_positions': [pos1, pos2, ...],  # Positions dans la séquence globale
                'parties_info': [
                    {
                        'partie_number': 1,
                        'debut_position': 0,
                        'fin_position': 67,
                        'nb_mains': 68,
                        'premiere_main_number': 1,
                        'derniere_main_number': 45
                    }, ...
                ],
                'nb_parties': X,
                'total_mains': Y
            }
        """
        if not hasattr(self, 'donnees') or not self.donnees:
            return {'erreur': 'Données non chargées'}

        if 'parties' not in self.donnees:
            return {'erreur': 'Structure de données invalide'}

        if not self.donnees['parties']:
            return {'erreur': 'Aucune partie trouvée'}

        frontieres_positions = []
        parties_info = []
        position_globale = 0

        for partie in self.donnees['parties']:
            partie_number = partie['partie_number']
            mains = partie['mains']

            if not mains:
                continue

            # 🔧 CORRECTION : DÉBUT = Première occurrence de main_number = 1
            premiere_main_1_index = None
            for i, main in enumerate(mains):
                if main['main_number'] == 1:
                    premiere_main_1_index = i
                    break

            if premiere_main_1_index is None:
                print(f"⚠️ ANOMALIE : Partie {partie_number}, aucune main_number = 1 trouvée")
                continue

            # 🔧 CORRECTION : FIN = Dernière main avec le main_number le plus élevé
            main_number_max = max(main['main_number'] for main in mains)
            derniere_main_max_index = None

            # Chercher la DERNIÈRE occurrence du main_number le plus élevé
            for i in range(len(mains) - 1, -1, -1):
                if mains[i]['main_number'] == main_number_max:
                    derniere_main_max_index = i
                    break

            if derniere_main_max_index is None:
                print(f"⚠️ ANOMALIE : Partie {partie_number}, impossible de trouver la dernière main max")
                continue

            # Informations de la partie avec positions précises
            debut_position = position_globale + premiere_main_1_index
            fin_position = position_globale + derniere_main_max_index
            nb_mains_effectives = derniere_main_max_index - premiere_main_1_index + 1

            # Vérifications de cohérence
            premiere_main = mains[premiere_main_1_index]
            derniere_main = mains[derniere_main_max_index]

            partie_info = {
                'partie_number': partie_number,
                'debut_position': debut_position,
                'fin_position': fin_position,
                'nb_mains': nb_mains_effectives,
                'premiere_main_number': premiere_main['main_number'],
                'premiere_main_result': premiere_main['index3_result'],
                'derniere_main_number': derniere_main['main_number'],
                'derniere_main_result': derniere_main['index3_result'],
                'main_number_max': main_number_max,
                'premiere_main_1_index': premiere_main_1_index,
                'derniere_main_max_index': derniere_main_max_index,
                'nb_mains_totales': len(mains)
            }

            parties_info.append(partie_info)

            # La frontière est APRÈS la dernière main effective de cette partie
            if len(parties_info) > 1:  # Pas de frontière avant la première partie
                frontieres_positions.append(fin_position)

            position_globale += len(mains)

        # Calculer le total de mains effectives
        total_mains_effectives = sum(p['nb_mains'] for p in parties_info)

        return {
            'frontieres_positions': frontieres_positions,
            'parties_info': parties_info,
            'nb_parties': len(parties_info),
            'total_mains': total_mains_effectives,
            'total_mains_brutes': position_globale,
            'methode': 'detection_frontieres_precises_avec_occurrences'
        }

    def _runs_test_parties_independantes(self, parties_sequences: list) -> dict:
        """
        Effectue un test des runs en respectant l'indépendance des parties
        WRAPPER vers lupasco_refactored.statistics.basic_stats.BasicStatistics.runs_test_parties_independantes

        Args:
            parties_sequences: Liste des séquences de chaque partie

        Returns:
            dict: Résultats du test des runs global
        """
        from lupasco_refactored.statistics.basic_stats import BasicStatistics

        stats = BasicStatistics()
        return stats.runs_test_parties_independantes(parties_sequences)

    def _detecter_cycles_avec_frontieres_precises(self, sequence: list, nom_index: str, max_periode: int = 50) -> dict:
        """
        Détecte les cycles en tenant compte des frontières exactes entre parties
        WRAPPER vers lupasco_refactored.statistics.transitions.TransitionAnalyzer.detecter_cycles_avec_frontieres_precises

        Args:
            sequence: Séquence à analyser (peut contenir des marqueurs)
            nom_index: Nom de l'index pour le rapport
            max_periode: Période maximale à tester

        Returns:
            dict: Résultats avec classification de fiabilité des cycles
        """
        from lupasco_refactored.statistics.transitions import TransitionAnalyzer

        # Obtenir les informations de frontières précises
        frontieres_info = self._detecter_frontieres_parties_precises()

        analyzer = TransitionAnalyzer()
        return analyzer.detecter_cycles_avec_frontieres_precises(sequence, nom_index, max_periode, frontieres_info)



    def _tester_stationnarite_avec_frontieres_precises(self, sequence: list, nom_index: str, nb_segments: int = 10) -> dict:
        """
        🔧 SOLUTION : Teste la stationnarité en respectant les frontières entre parties

        Args:
            sequence: Séquence à analyser
            nom_index: Nom de l'index pour le rapport
            nb_segments: Nombre de segments souhaités

        Returns:
            dict: Résultats avec analyses intra-parties et inter-parties
        """
        # Obtenir les informations de frontières précises
        frontieres_info = self._detecter_frontieres_parties_precises()

        if 'erreur' in frontieres_info:
            # Fallback : utiliser la méthode de division par marqueurs
            parties_sequences = self._diviser_en_parties(sequence)
        else:
            # Diviser la séquence selon les frontières détectées
            sequence_complete = [x for x in sequence if x != "__FIN_PARTIE__"]
            parties_sequences = []
            debut = 0

            for frontiere_pos in frontieres_info['frontieres_positions']:
                partie_seq = sequence_complete[debut:frontiere_pos + 1]
                parties_sequences.append(partie_seq)
                debut = frontiere_pos + 1

            # Ajouter la dernière partie
            if debut < len(sequence_complete):
                partie_seq = sequence_complete[debut:]
                parties_sequences.append(partie_seq)

        # 1. ANALYSE INTRA-PARTIES : Stationnarité DANS chaque partie
        stationnarite_intra_parties = []
        for i, partie_seq in enumerate(parties_sequences):
            if len(partie_seq) >= 50:  # Assez de données pour analyse
                # Diviser la partie en segments
                segment_size = max(10, len(partie_seq) // min(nb_segments, len(partie_seq) // 10))
                segments_partie = []

                for j in range(0, len(partie_seq), segment_size):
                    segment = partie_seq[j:j + segment_size]
                    if len(segment) >= 10:  # Segment assez grand
                        segments_partie.append(segment)

                if len(segments_partie) >= 2:
                    # Calculer l'entropie de chaque segment
                    entropies_segments = []
                    for segment in segments_partie:
                        entropie = shannon_entropy_from_data(segment)
                        entropies_segments.append(entropie)

                    # Test de stationnarité intra-partie
                    cv_entropies = coefficient_of_variation(entropies_segments)

                    stationnarite_intra_parties.append({
                        'partie_id': i + 1,
                        'nb_segments': len(segments_partie),
                        'entropies_segments': entropies_segments,
                        'cv_entropies': cv_entropies,
                        'stationnaire_intra': cv_entropies < 0.1,
                        'taille_partie': len(partie_seq)
                    })

        # 2. ANALYSE INTER-PARTIES : Stationnarité ENTRE les parties
        entropies_parties = []
        for partie_seq in parties_sequences:
            if len(partie_seq) > 0:
                entropie_partie = shannon_entropy_from_data(partie_seq)
                entropies_parties.append(entropie_partie)

        stationnarite_inter_parties = {}
        if len(entropies_parties) >= 2:
            cv_entre_parties = coefficient_of_variation(entropies_parties)

            # Test des runs sur les entropies des parties
            entropies_binaires = [1 if e > np.median(entropies_parties) else 0 for e in entropies_parties]
            runs_entropies = runs_test(entropies_binaires)

            stationnarite_inter_parties = {
                'entropies_parties': entropies_parties,
                'cv_entre_parties': cv_entre_parties,
                'runs_test_entropies': runs_entropies,
                'stationnaire_inter': cv_entre_parties < 0.1 and runs_entropies['pvalue'] > 0.05
            }

        return {
            'stationnarite_intra_parties': stationnarite_intra_parties,
            'stationnarite_inter_parties': stationnarite_inter_parties,
            'nb_parties_analysees': len(parties_sequences),
            'methode': f'test_stationnarite_avec_frontieres_precises_{nom_index}',
            'parties_avec_donnees_suffisantes': len(stationnarite_intra_parties)
        }

    def _calculer_entropie_locale(self, sequence: list, combinaison: str, taille_fenetre: int = 100) -> float:
        """
        Calcule l'entropie locale dans des fenêtres glissantes pour une combinaison donnée
        🔧 CORRECTION : Respecte les frontières des parties (main_number=1 à main_number=max)
        """
        # 🔧 CORRECTION : Diviser la séquence en parties indépendantes
        parties_sequences = self._diviser_en_parties(sequence)

        if not parties_sequences:
            return 0.0

        entropies_toutes_parties = []

        # Analyser chaque partie séparément
        for partie_seq in parties_sequences:
            if not partie_seq:
                continue

            # 🔧 CORRECTION : Adapter la fenêtre à la longueur de CETTE partie
            # Chaque partie = de main_number=1 à main_number=max (au moins 60 mains)
            longueur_partie = len(partie_seq)

            # Fenêtre adaptée : minimum 5, maximum taille_fenetre, optimal = 1/4 de la partie
            taille_adaptee = min(taille_fenetre, max(5, longueur_partie // 4))

            if longueur_partie < taille_adaptee:
                # Partie trop courte : calculer fréquence globale dans la partie
                freq_partie = partie_seq.count(combinaison) / longueur_partie if longueur_partie > 0 else 0
                if freq_partie > 0 and freq_partie < 1:
                    entropie_partie = -(freq_partie * np.log2(freq_partie) +
                                      (1 - freq_partie) * np.log2(1 - freq_partie))
                    entropies_toutes_parties.append(entropie_partie)
                continue

            # Fenêtres glissantes DANS cette partie uniquement
            entropies_partie = []
            for i in range(longueur_partie - taille_adaptee + 1):
                fenetre = partie_seq[i:i + taille_adaptee]
                freq_combo = fenetre.count(combinaison) / taille_adaptee

                if freq_combo > 0:
                    if freq_combo < 1:
                        entropie_bin = -(freq_combo * np.log2(freq_combo) +
                                       (1 - freq_combo) * np.log2(1 - freq_combo))
                    else:
                        entropie_bin = 0
                    entropies_partie.append(entropie_bin)

            # Ajouter la moyenne de cette partie
            if entropies_partie:
                entropies_toutes_parties.append(np.mean(entropies_partie))

        return np.mean(entropies_toutes_parties) if entropies_toutes_parties else 0.0

    def analyser_statistiques_avancees(self, sequence: list, nom_sequence: str) -> dict:
        """
        Analyse statistique complète avec les nouvelles méthodes validées

        Args:
            sequence: Séquence à analyser
            nom_sequence: Nom de la séquence

        Returns:
            dict: Résultats de l'analyse statistique avancée
        """
        print(f"\n📊 Analyse statistique avancée : {nom_sequence}")

        # Diviser en parties indépendantes
        parties_sequences = self._diviser_en_parties(sequence)

        if not parties_sequences:
            return {'erreur': 'Aucune partie trouvée'}

        # Analyser chaque partie séparément
        resultats_parties = []

        for i, partie_seq in enumerate(parties_sequences):
            if not partie_seq or len(partie_seq) < 5:
                continue

            # Compter les fréquences
            compteur = Counter(partie_seq)
            valeurs = list(compteur.keys())
            frequences = list(compteur.values())

            # Analyse de cette partie
            analyse_partie = {
                'partie_numero': i + 1,
                'longueur': len(partie_seq),
                'valeurs_uniques': len(valeurs),
                'distribution': dict(compteur)
            }

            # 1. ENTROPIE DE SHANNON (globale pour cette partie)
            try:
                analyse_partie['entropie_shannon'] = shannon_entropy_from_data(partie_seq)
            except Exception as e:
                analyse_partie['entropie_shannon'] = 0.0
                analyse_partie['erreur_entropie'] = str(e)

            # 2. COEFFICIENT DE GINI (pour mesurer l'inégalité de distribution)
            try:
                analyse_partie['coefficient_gini'] = gini_coefficient(frequences)
            except Exception as e:
                analyse_partie['coefficient_gini'] = 0.0
                analyse_partie['erreur_gini'] = str(e)

            # 3. COEFFICIENT DE VARIATION (variabilité relative)
            try:
                analyse_partie['coefficient_variation'] = coefficient_of_variation(frequences)
            except Exception as e:
                analyse_partie['coefficient_variation'] = 0.0
                analyse_partie['erreur_cv'] = str(e)

            # 4. DÉTECTION D'ANOMALIES (Z-score)
            try:
                anomalies = detect_anomalies(frequences, threshold=2.0)
                analyse_partie['anomalies'] = {
                    'nb_anomalies': len(anomalies['anomalies_indices']),
                    'indices_anomalies': anomalies['anomalies_indices'].tolist(),
                    'valeurs_anormales': anomalies['anomalies_values'].tolist(),
                    'z_scores_max': float(np.max(np.abs(anomalies['z_scores']))) if len(anomalies['z_scores']) > 0 else 0.0
                }
            except Exception as e:
                analyse_partie['anomalies'] = {'erreur': str(e)}

            # 5. AUTOCORRÉLATION (dépendances temporelles)
            try:
                # Convertir en numérique pour l'autocorrélation
                valeurs_numeriques = self._convertir_sequence_numerique(partie_seq)
                if len(valeurs_numeriques) > 10:
                    autocorr = autocorrelation_function(valeurs_numeriques, max_lag=min(10, len(valeurs_numeriques)//4))
                    analyse_partie['autocorrelation'] = {
                        'lag_1': float(autocorr[1]) if len(autocorr) > 1 else 0.0,
                        'lag_max': float(np.max(np.abs(autocorr[1:]))) if len(autocorr) > 1 else 0.0,
                        'autocorr_significative': float(np.max(np.abs(autocorr[1:]))) > 0.1 if len(autocorr) > 1 else False
                    }
                else:
                    analyse_partie['autocorrelation'] = {'erreur': 'Séquence trop courte'}
            except Exception as e:
                analyse_partie['autocorrelation'] = {'erreur': str(e)}

            resultats_parties.append(analyse_partie)

        # Synthèse globale
        if resultats_parties:
            entropies = [p['entropie_shannon'] for p in resultats_parties if 'entropie_shannon' in p]
            ginis = [p['coefficient_gini'] for p in resultats_parties if 'coefficient_gini' in p]
            cvs = [p['coefficient_variation'] for p in resultats_parties if 'coefficient_variation' in p]

            synthese = {
                'nb_parties_analysees': len(resultats_parties),
                'entropie_moyenne': float(np.mean(entropies)) if entropies else 0.0,
                'entropie_std': float(np.std(entropies)) if entropies else 0.0,
                'gini_moyen': float(np.mean(ginis)) if ginis else 0.0,
                'cv_moyen': float(np.mean(cvs)) if cvs else 0.0,
                'variabilite_entropie': float(np.std(entropies)) if entropies else 0.0
            }
        else:
            synthese = {'erreur': 'Aucune partie analysable'}

        return {
            'sequence': nom_sequence,
            'methode': 'analyse_statistique_avancee_lupasco',
            'synthese_globale': synthese,
            'details_parties': resultats_parties[:5],  # Limiter à 5 parties pour le rapport
            'nb_parties_total': len(parties_sequences)
        }

    def _convertir_sequence_numerique(self, sequence: list) -> list:
        """
        Convertit une séquence de chaînes en valeurs numériques pour l'autocorrélation
        WRAPPER vers lupasco_refactored.utils.data_utils.convertir_sequence_numerique

        Args:
            sequence: Séquence de chaînes

        Returns:
            list: Séquence numérique
        """
        from lupasco_refactored.utils.data_utils import convertir_sequence_numerique
        return convertir_sequence_numerique(sequence)

    def _diviser_en_parties(self, sequence: list) -> list:
        """
        🔧 CORRECTION : Divise une séquence en parties indépendantes basées sur les frontières précises

        Args:
            sequence: Séquence globale avec marqueurs __FIN_PARTIE__

        Returns:
            list: Liste des séquences de chaque partie (sans marqueurs)
        """
        if not sequence:
            return []

        parties_sequences = []
        partie_courante = []

        for element in sequence:
            if element == "__FIN_PARTIE__":
                if partie_courante:
                    parties_sequences.append(partie_courante)
                    partie_courante = []
            else:
                partie_courante.append(element)

        # Ajouter la dernière partie si elle n'est pas vide
        if partie_courante:
            parties_sequences.append(partie_courante)

        return parties_sequences

    def _analyser_tendances_avec_frontieres_precises(self, sequence: list, nom_index: str, n_periodes: int = 5) -> dict:
        """
        🔧 SOLUTION : Analyse les tendances en tenant compte des frontières entre parties

        Args:
            sequence: Séquence à analyser
            nom_index: Nom de l'index pour le rapport
            n_periodes: Nombre de périodes pour l'analyse globale

        Returns:
            dict: Tendances intra-parties, inter-parties et globales
        """
        # Obtenir les informations de frontières précises
        frontieres_info = self._detecter_frontieres_parties_precises()

        if 'erreur' in frontieres_info:
            # Fallback : utiliser la méthode de division par marqueurs
            parties_sequences = self._diviser_en_parties(sequence)
            sequence_complete = []
            for partie_seq in parties_sequences:
                sequence_complete.extend(partie_seq)
        else:
            # Diviser la séquence selon les frontières détectées
            sequence_complete = [x for x in sequence if x != "__FIN_PARTIE__"]
            parties_sequences = []
            debut = 0

            for frontiere_pos in frontieres_info['frontieres_positions']:
                partie_seq = sequence_complete[debut:frontiere_pos + 1]
                parties_sequences.append(partie_seq)
                debut = frontiere_pos + 1

            # Ajouter la dernière partie
            if debut < len(sequence_complete):
                partie_seq = sequence_complete[debut:]
                parties_sequences.append(partie_seq)

        combinaisons = sorted(set(sequence_complete))

        # 1. TENDANCES INTER-PARTIES : Évolution d'une partie à l'autre
        tendances_inter_parties = {}

        for combo in combinaisons:
            # Fréquence dans chaque partie
            frequences_par_partie = []
            for partie_seq in parties_sequences:
                if len(partie_seq) > 0:
                    freq = partie_seq.count(combo) / len(partie_seq)
                    frequences_par_partie.append(freq)
                else:
                    frequences_par_partie.append(0)

            # Tendance temporelle entre parties
            if len(frequences_par_partie) > 1:
                temps_parties = list(range(len(frequences_par_partie)))
                if len(set(frequences_par_partie)) > 1:  # Éviter division par zéro
                    correlation_temps = np.corrcoef(temps_parties, frequences_par_partie)[0, 1]
                else:
                    correlation_temps = 0
            else:
                correlation_temps = 0

            tendances_inter_parties[combo] = {
                'frequences_par_partie': frequences_par_partie,
                'correlation_temps_parties': correlation_temps,
                'nb_parties_presentes': sum(1 for f in frequences_par_partie if f > 0),
                'tendance_entre_parties': 'croissante' if correlation_temps > 0.3 else
                                        'décroissante' if correlation_temps < -0.3 else 'stable'
            }

        # 2. TENDANCES GLOBALES : Analyse sur toute la séquence avec conscience des frontières
        tendances_globales = {}

        # Diviser la séquence complète en périodes
        taille_periode = len(sequence_complete) // n_periodes

        for combo in combinaisons:
            frequences_periodes = []

            for i in range(n_periodes):
                start = i * taille_periode
                end = start + taille_periode if i < n_periodes - 1 else len(sequence_complete)
                periode = sequence_complete[start:end]

                freq = periode.count(combo) / len(periode) if len(periode) > 0 else 0
                frequences_periodes.append(freq)

            # Calculer la tendance globale
            temps = list(range(n_periodes))
            if len(set(frequences_periodes)) > 1:
                correlation_temps_global = np.corrcoef(temps, frequences_periodes)[0, 1]
            else:
                correlation_temps_global = 0

            # Coefficient de variation temporel
            cv_temporel = coefficient_of_variation(frequences_periodes)

            # Compter combien de frontières sont traversées par les périodes
            frontieres_traversees = 0
            if 'frontieres_positions' in frontieres_info:
                for frontiere in frontieres_info['frontieres_positions']:
                    for i in range(n_periodes):
                        start = i * taille_periode
                        end = start + taille_periode if i < n_periodes - 1 else len(sequence_complete)
                        if start <= frontiere < end:
                            frontieres_traversees += 1
                            break

            tendances_globales[combo] = {
                'frequences_periodes': frequences_periodes,
                'correlation_temps_global': correlation_temps_global,
                'cv_temporel': cv_temporel,
                'tendance_globale': 'croissante' if correlation_temps_global > 0.3 else
                                   'décroissante' if correlation_temps_global < -0.3 else 'stable',
                'frontieres_traversees_par_periodes': frontieres_traversees,
                'fiabilite_tendance': 'moyenne' if frontieres_traversees > 0 else 'haute'
            }

        return {
            'tendances_inter_parties': tendances_inter_parties,
            'tendances_globales': tendances_globales,
            'nb_parties_analysees': len(parties_sequences),
            'nb_periodes_globales': n_periodes,
            'methode': f'analyse_tendances_avec_frontieres_precises_{nom_index}'
        }

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX5 - TRANSITIONS
    # --------------------------------------------------------------------------------

    def _analyser_transitions_index5(self, sequence: list) -> dict:
        """
        Analyse les transitions entre combinaisons INDEX5 avec formules exactes
        WRAPPER vers lupasco_refactored.statistics.transitions.TransitionAnalyzer.analyser_transitions
        Respecte l'indépendance des parties
        """
        from lupasco_refactored.statistics.transitions import TransitionAnalyzer

        analyzer = TransitionAnalyzer()
        return analyzer.analyser_transitions(sequence, "INDEX5")

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX5 - PATTERNS TEMPORELS
    # --------------------------------------------------------------------------------

    def _analyser_patterns_temporels_index5(self, sequence: list) -> dict:
        """
        Analyse les patterns temporels dans INDEX5 avec formules exactes
        """
        print("   ⏰ Analyse des patterns temporels...")

        # 1. Analyse des cycles et périodicités
        cycles = self._detecter_cycles_index5(sequence)

        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_index5(sequence)

        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_index5(sequence)

        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }

    def _detecter_cycles_index5(self, sequence: list, max_periode: int = 50) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Détecte les cycles INDEX5 avec compteurs de frontières précises
        """
        return self._detecter_cycles_avec_frontieres_precises(sequence, 'INDEX5', max_periode)

    def _tester_stationnarite_index5(self, sequence: list, nb_segments: int = 10) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Teste la stationnarité INDEX5 avec compteurs de frontières précises
        """
        return self._tester_stationnarite_avec_frontieres_precises(sequence, 'INDEX5', nb_segments)

    def _analyser_tendances_index5(self, sequence: list) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Analyse les tendances INDEX5 avec compteurs de frontières précises
        """
        return self._analyser_tendances_avec_frontieres_precises(sequence, 'INDEX5')

    # ================================================================================
    # 🎯 MÉTHODES D'ANALYSE INDEX5 (18 COMBINAISONS)
    # ================================================================================
    # INDEX5 = SYNC/DESYNC + pair_4/pair_6/impair_5 + BANKER/PLAYER/TIE
    # Format: "SYNC_pair_4_BANKER", "DESYNC_impair_5_PLAYER", etc.
    # ================================================================================

    def analyser_index5_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX5 avec toutes les formules mathématiques exactes
        pour chacune des 18 combinaisons possibles
        WRAPPER vers lupasco_refactored.analyzers.index5_analyzer.Index5Analyzer.analyser_index5_avec_formules_exactes
        """
        from lupasco_refactored.analyzers.index5_analyzer import Index5Analyzer

        analyzer = Index5Analyzer(self.sequences)
        resultats = analyzer.analyser_index5_avec_formules_exactes()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX5_FORMULES_EXACTES'] = resultats

        return resultats

    def _entropie_renyi(self, data, alpha):
        """
        Calcule l'entropie de Rényi d'ordre α
        WRAPPER vers lupasco_refactored.utils.math_formulas.entropie_renyi

        Args:
            data: Séquence de données
            alpha: Paramètre d'ordre (α ≠ 1)

        Returns:
            float: Entropie de Rényi en bits
        """
        from lupasco_refactored.utils.math_formulas import entropie_renyi
        return entropie_renyi(data, alpha)

    def _analyser_entropie_renyi_tous_indices(self):
        """
        Analyse Rényi pour INDEX5, INDEX2_INDEX3, INDEX1_INDEX3
        WRAPPER vers lupasco_refactored.statistics.entropy_advanced.AdvancedEntropy.analyser_entropie_renyi_tous_indices

        Returns:
            dict: Résultats Rényi pour chaque indice
        """
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy

        analyzer = AdvancedEntropy()
        return analyzer.analyser_entropie_renyi_tous_indices(self.sequences)

    def _entropie_conditionnelle(self, Y, X):
        """
        Calcule l'entropie conditionnelle H(Y|X)
        WRAPPER vers lupasco_refactored.utils.math_formulas.entropie_conditionnelle

        Args:
            Y: Variable dépendante (ex: INDEX3)
            X: Variable conditionnelle (ex: INDEX1 ou INDEX2)

        Returns:
            float: Entropie conditionnelle en bits
        """
        from lupasco_refactored.utils.math_formulas import entropie_conditionnelle
        return entropie_conditionnelle(Y, X)

    def _analyser_entropie_conditionnelle_prediction(self):
        """
        Analyse conditionnelle pour optimiser la prédiction de INDEX3
        WRAPPER vers lupasco_refactored.statistics.entropy_advanced.AdvancedEntropy.analyser_entropie_conditionnelle_prediction

        Returns:
            dict: Résultats de l'analyse conditionnelle
        """
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy

        # Extraire les composants depuis INDEX5
        composants = self._extraire_composants_index5()

        analyzer = AdvancedEntropy()
        return analyzer.analyser_entropie_conditionnelle_prediction(composants)

    def _entropie_jointe(self, X, Y):
        """
        Calcule l'entropie jointe H(X,Y)
        WRAPPER vers lupasco_refactored.utils.math_formulas.entropie_jointe

        Args:
            X, Y: Variables aléatoires

        Returns:
            float: Entropie jointe en bits
        """
        from lupasco_refactored.utils.math_formulas import entropie_jointe
        return entropie_jointe(X, Y)

    def _information_mutuelle(self, X, Y):
        """
        Calcule l'information mutuelle I(X;Y) = H(X) + H(Y) - H(X,Y)
        WRAPPER vers lupasco_refactored.utils.math_formulas.information_mutuelle

        Args:
            X, Y: Variables aléatoires

        Returns:
            float: Information mutuelle en bits
        """
        from lupasco_refactored.utils.math_formulas import information_mutuelle
        return information_mutuelle(X, Y)

    def _analyser_information_mutuelle_complete(self):
        """
        Analyse complète de l'information mutuelle entre tous les indices
        WRAPPER vers lupasco_refactored.statistics.entropy_advanced.AdvancedEntropy.analyser_information_mutuelle_complete

        Returns:
            dict: Résultats de l'information mutuelle
        """
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy

        # Extraire les composants depuis INDEX5
        composants = self._extraire_composants_index5()

        analyzer = AdvancedEntropy()
        return analyzer.analyser_information_mutuelle_complete(self.sequences, composants)

    def _entropie_croisee(self, p_reelle, p_theorique):
        """
        Calcule l'entropie croisée H(p,q) = -Σ p(i) × log(q(i))
        WRAPPER vers lupasco_refactored.utils.math_formulas.entropie_croisee

        Args:
            p_reelle: Distribution réelle observée
            p_theorique: Distribution théorique de référence

        Returns:
            float: Entropie croisée en bits
        """
        from lupasco_refactored.utils.math_formulas import entropie_croisee
        return entropie_croisee(p_reelle, p_theorique)

    def _calculer_distribution(self, data, nb_valeurs_possibles):
        """
        Calcule la distribution de probabilité d'une séquence
        WRAPPER vers lupasco_refactored.utils.data_utils.calculer_distribution

        Args:
            data: Séquence de données
            nb_valeurs_possibles: Nombre total de valeurs possibles

        Returns:
            list: Distribution de probabilité
        """
        from lupasco_refactored.utils.data_utils import calculer_distribution
        return calculer_distribution(data, nb_valeurs_possibles)

    def _analyser_entropie_croisee_tous_indices(self):
        """
        Analyse entropie croisée pour détecter les déviations du hasard
        ADAPTÉ POUR ANALYSEUR.PY

        Returns:
            dict: Résultats de l'entropie croisée pour chaque indice
        """
        import math

        resultats = {}

        # Distributions théoriques uniformes
        distributions_uniformes = {
            'INDEX5': [1/18] * 18,
            'INDEX2_INDEX3': [1/9] * 9,
            'INDEX1_INDEX3': [1/6] * 6
        }

        # INDEX5 - utiliser self.sequences directement
        index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
        dist_reelle_index5 = self._calculer_distribution(index5_data, 18)
        h_croisee_index5 = self._entropie_croisee(dist_reelle_index5, distributions_uniformes['INDEX5'])
        h_shannon_index5 = self.calculer_entropie_shannon(index5_data)

        resultats['INDEX5'] = {
            'entropie_croisee': h_croisee_index5,
            'entropie_shannon': h_shannon_index5,
            'divergence_kl': h_croisee_index5 - h_shannon_index5,
            'deviation_hasard': (h_croisee_index5 - math.log2(18)) / math.log2(18)
        }

        # INDEX2_INDEX3 - utiliser self.sequences directement
        index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        dist_reelle_index2_index3 = self._calculer_distribution(index2_index3_data, 9)
        h_croisee_index2_index3 = self._entropie_croisee(dist_reelle_index2_index3, distributions_uniformes['INDEX2_INDEX3'])
        h_shannon_index2_index3 = self.calculer_entropie_shannon(index2_index3_data)

        resultats['INDEX2_INDEX3'] = {
            'entropie_croisee': h_croisee_index2_index3,
            'entropie_shannon': h_shannon_index2_index3,
            'divergence_kl': h_croisee_index2_index3 - h_shannon_index2_index3,
            'deviation_hasard': (h_croisee_index2_index3 - math.log2(9)) / math.log2(9)
        }

        # INDEX1_INDEX3 - utiliser self.sequences directement
        index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        dist_reelle_index1_index3 = self._calculer_distribution(index1_index3_data, 6)
        h_croisee_index1_index3 = self._entropie_croisee(dist_reelle_index1_index3, distributions_uniformes['INDEX1_INDEX3'])
        h_shannon_index1_index3 = self.calculer_entropie_shannon(index1_index3_data)

        resultats['INDEX1_INDEX3'] = {
            'entropie_croisee': h_croisee_index1_index3,
            'entropie_shannon': h_shannon_index1_index3,
            'divergence_kl': h_croisee_index1_index3 - h_shannon_index1_index3,
            'deviation_hasard': (h_croisee_index1_index3 - math.log2(6)) / math.log2(6)
        }

        return resultats

    def _kl_divergence(self, p, q):
        """
        Calcule la divergence de Kullback-Leibler D_KL(P||Q)
        WRAPPER vers lupasco_refactored.utils.math_formulas.kl_divergence

        Args:
            p: Distribution de probabilité P
            q: Distribution de probabilité Q

        Returns:
            float: Divergence KL en bits
        """
        from lupasco_refactored.utils.math_formulas import kl_divergence
        return kl_divergence(p, q)

    def _calculer_profils_reference_tous_indices(self):
        """
        Calcule les profils de référence moyens pour tous les indices
        🔧 CORRECTION : Adapté pour analyseur.py avec distributions uniformes

        Returns:
            dict: Profils de référence pour chaque indice
        """
        import numpy as np

        # 🔧 CORRECTION : Utiliser des distributions uniformes comme référence
        # car self.donnees['parties'] n'existe pas dans le contexte streaming
        profils = {
            'INDEX5': np.ones(18) / 18,        # Distribution uniforme 18 combinaisons
            'INDEX2_INDEX3': np.ones(9) / 9,   # Distribution uniforme 9 combinaisons
            'INDEX1_INDEX3': np.ones(6) / 6    # Distribution uniforme 6 combinaisons
        }

        print("   🔧 Utilisation de profils de référence uniformes")
        print(f"      INDEX5: {len(profils['INDEX5'])} combinaisons")
        print(f"      INDEX2_INDEX3: {len(profils['INDEX2_INDEX3'])} combinaisons")
        print(f"      INDEX1_INDEX3: {len(profils['INDEX1_INDEX3'])} combinaisons")

        return profils

    def _analyser_divergence_kl_comparative(self, profils_reference):
        """
        Compare les séquences actuelles aux profils de référence via divergence KL
        ADAPTÉ POUR ANALYSEUR.PY

        Args:
            profils_reference: Profils de référence calculés

        Returns:
            dict: Résultats de la divergence KL pour chaque indice
        """
        resultats = {}

        # INDEX5 - utiliser self.sequences directement
        index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
        dist_partie_index5 = self._calculer_distribution(index5_data, 18)
        div_kl_index5 = self._kl_divergence(dist_partie_index5, profils_reference['INDEX5'])
        resultats['INDEX5'] = {
            'divergence_kl': div_kl_index5,
            'classification': self._classifier_selon_divergence(div_kl_index5, 'INDEX5')
        }

        # INDEX2_INDEX3 - utiliser self.sequences directement
        index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        dist_partie_index2_index3 = self._calculer_distribution(index2_index3_data, 9)
        div_kl_index2_index3 = self._kl_divergence(dist_partie_index2_index3, profils_reference['INDEX2_INDEX3'])
        resultats['INDEX2_INDEX3'] = {
            'divergence_kl': div_kl_index2_index3,
            'classification': self._classifier_selon_divergence(div_kl_index2_index3, 'INDEX2_INDEX3')
        }

        # INDEX1_INDEX3 - utiliser self.sequences directement
        index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        dist_partie_index1_index3 = self._calculer_distribution(index1_index3_data, 6)
        div_kl_index1_index3 = self._kl_divergence(dist_partie_index1_index3, profils_reference['INDEX1_INDEX3'])
        resultats['INDEX1_INDEX3'] = {
            'divergence_kl': div_kl_index1_index3,
            'classification': self._classifier_selon_divergence(div_kl_index1_index3, 'INDEX1_INDEX3')
        }

        # Score composite d'anomalie
        score_anomalie = (
            resultats['INDEX5']['divergence_kl'] * 0.5 +
            resultats['INDEX2_INDEX3']['divergence_kl'] * 0.3 +
            resultats['INDEX1_INDEX3']['divergence_kl'] * 0.2
        )

        resultats['score_anomalie_composite'] = score_anomalie

        return resultats

    def _classifier_selon_divergence(self, divergence, type_indice):
        """
        Classifie selon la divergence KL

        Args:
            divergence: Valeur de divergence KL
            type_indice: Type d'indice ('INDEX5', 'INDEX2_INDEX3', 'INDEX1_INDEX3')

        Returns:
            str: Classification
        """
        seuils = {
            'INDEX5': {'normal': 0.5, 'anormal': 1.0, 'extreme': 2.0},
            'INDEX2_INDEX3': {'normal': 0.3, 'anormal': 0.7, 'extreme': 1.5},
            'INDEX1_INDEX3': {'normal': 0.2, 'anormal': 0.5, 'extreme': 1.0}
        }

        s = seuils[type_indice]

        if divergence < s['normal']:
            return "NORMAL"
        elif divergence < s['anormal']:
            return "ANORMAL"
        elif divergence < s['extreme']:
            return "TRES_ANORMAL"
        else:
            return "EXTREME"

    def _analyse_temporelle_multi_indices(self, taille_fenetre=20):
        """
        Analyse temporelle avec fenêtres glissantes pour tous les indices

        Args:
            taille_fenetre: Taille de la fenêtre glissante

        Returns:
            dict: Évolution temporelle de l'entropie pour chaque indice
        """
        import numpy as np

        resultats = {}

        # INDEX5 - utiliser self.sequences directement
        index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
        entropies_index5 = []

        for i in range(len(index5_data) - taille_fenetre + 1):
            fenetre = index5_data[i:i+taille_fenetre]
            h = self.calculer_entropie_shannon(fenetre)
            entropies_index5.append(h)

        resultats['INDEX5'] = {
            'entropies_temporelles': entropies_index5,
            'changements_regime': self._detecter_changements_regime(entropies_index5),
            'variance_temporelle': np.var(entropies_index5) if entropies_index5 else 0,
            'tendance': self._analyser_tendance(entropies_index5)
        }

        # INDEX2_INDEX3 - utiliser self.sequences directement
        index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        entropies_index2_index3 = []

        for i in range(len(index2_index3_data) - taille_fenetre + 1):
            fenetre = index2_index3_data[i:i+taille_fenetre]
            h = self.calculer_entropie_shannon(fenetre)
            entropies_index2_index3.append(h)

        resultats['INDEX2_INDEX3'] = {
            'entropies_temporelles': entropies_index2_index3,
            'changements_regime': self._detecter_changements_regime(entropies_index2_index3),
            'variance_temporelle': np.var(entropies_index2_index3) if entropies_index2_index3 else 0,
            'tendance': self._analyser_tendance(entropies_index2_index3)
        }

        # INDEX1_INDEX3 - utiliser self.sequences directement
        index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        entropies_index1_index3 = []

        for i in range(len(index1_index3_data) - taille_fenetre + 1):
            fenetre = index1_index3_data[i:i+taille_fenetre]
            h = self.calculer_entropie_shannon(fenetre)
            entropies_index1_index3.append(h)

        resultats['INDEX1_INDEX3'] = {
            'entropies_temporelles': entropies_index1_index3,
            'changements_regime': self._detecter_changements_regime(entropies_index1_index3),
            'variance_temporelle': np.var(entropies_index1_index3) if entropies_index1_index3 else 0,
            'tendance': self._analyser_tendance(entropies_index1_index3)
        }

        return resultats

    def _detecter_changements_regime(self, entropies, seuil_changement=0.3):
        """
        Détecte les changements de régime dans une série d'entropies

        Args:
            entropies: Liste des entropies temporelles
            seuil_changement: Seuil pour détecter un changement

        Returns:
            list: Indices des changements de régime
        """
        if len(entropies) < 3:
            return []

        changements = []

        for i in range(1, len(entropies) - 1):
            # Calculer la différence avec les voisins
            diff_avant = abs(entropies[i] - entropies[i-1])
            diff_apres = abs(entropies[i+1] - entropies[i])

            # Détecter un changement significatif
            if diff_avant > seuil_changement or diff_apres > seuil_changement:
                changements.append(i)

        return changements

    def _analyser_tendance(self, entropies):
        """
        Analyse la tendance générale d'une série d'entropies

        Args:
            entropies: Liste des entropies temporelles

        Returns:
            str: Type de tendance
        """
        import numpy as np

        if len(entropies) < 2:
            return "INSUFFISANT"

        # Régression linéaire simple
        x = np.arange(len(entropies))
        y = np.array(entropies)

        # Calculer la pente
        pente = np.polyfit(x, y, 1)[0]

        if pente > 0.01:
            return "CROISSANTE"
        elif pente < -0.01:
            return "DECROISSANTE"
        else:
            return "STABLE"

    def _construire_matrice_transitions(self, sequence):
        """
        Construit la matrice de transitions d'une séquence

        Args:
            sequence: Séquence de données

        Returns:
            tuple: (matrice, etats_uniques)
        """
        import numpy as np

        # Identifier les états uniques
        etats_uniques = sorted(set(sequence))
        nb_etats = len(etats_uniques)

        # Créer un mapping état -> indice
        etat_to_index = {etat: i for i, etat in enumerate(etats_uniques)}

        # Initialiser la matrice
        matrice = np.zeros((nb_etats, nb_etats))

        # Compter les transitions
        for i in range(len(sequence) - 1):
            etat_actuel = sequence[i]
            etat_suivant = sequence[i + 1]

            idx_actuel = etat_to_index[etat_actuel]
            idx_suivant = etat_to_index[etat_suivant]

            matrice[idx_actuel, idx_suivant] += 1

        # Normaliser par ligne (probabilités conditionnelles)
        for i in range(nb_etats):
            somme_ligne = np.sum(matrice[i, :])
            if somme_ligne > 0:
                matrice[i, :] /= somme_ligne

        return matrice, etats_uniques

    def _analyse_transitions_tous_indices(self):
        """
        Analyse des transitions pour tous les indices

        Returns:
            dict: Résultats de l'analyse des transitions
        """
        import numpy as np
        import math

        resultats = {}

        # INDEX5 - utiliser self.sequences directement
        index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
        matrice_index5, etats_index5 = self._construire_matrice_transitions(index5_data)

        # Calculer l'entropie de chaque état
        entropies_etats_index5 = []
        for i in range(len(etats_index5)):
            probs_suivants = matrice_index5[i, :]
            h = 0.0
            for p in probs_suivants:
                if p > 0:
                    h -= p * math.log2(p)
            entropies_etats_index5.append(h)

        resultats['INDEX5'] = {
            'matrice_transitions': matrice_index5.tolist(),
            'etats': etats_index5,
            'entropies_etats': entropies_etats_index5,
            'etats_predictibles': [i for i, h in enumerate(entropies_etats_index5) if h < 1.0],
            'entropie_moyenne_transitions': np.mean(entropies_etats_index5)
        }

        # INDEX2_INDEX3 - utiliser self.sequences directement
        index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        matrice_index2_index3, etats_index2_index3 = self._construire_matrice_transitions(index2_index3_data)

        entropies_etats_index2_index3 = []
        for i in range(len(etats_index2_index3)):
            probs_suivants = matrice_index2_index3[i, :]
            h = 0.0
            for p in probs_suivants:
                if p > 0:
                    h -= p * math.log2(p)
            entropies_etats_index2_index3.append(h)

        resultats['INDEX2_INDEX3'] = {
            'matrice_transitions': matrice_index2_index3.tolist(),
            'etats': etats_index2_index3,
            'entropies_etats': entropies_etats_index2_index3,
            'etats_predictibles': [i for i, h in enumerate(entropies_etats_index2_index3) if h < 1.0],
            'entropie_moyenne_transitions': np.mean(entropies_etats_index2_index3)
        }

        # INDEX1_INDEX3 - utiliser self.sequences directement
        index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        matrice_index1_index3, etats_index1_index3 = self._construire_matrice_transitions(index1_index3_data)

        entropies_etats_index1_index3 = []
        for i in range(len(etats_index1_index3)):
            probs_suivants = matrice_index1_index3[i, :]
            h = 0.0
            for p in probs_suivants:
                if p > 0:
                    h -= p * math.log2(p)
            entropies_etats_index1_index3.append(h)

        resultats['INDEX1_INDEX3'] = {
            'matrice_transitions': matrice_index1_index3.tolist(),
            'etats': etats_index1_index3,
            'entropies_etats': entropies_etats_index1_index3,
            'etats_predictibles': [i for i, h in enumerate(entropies_etats_index1_index3) if h < 1.0],
            'entropie_moyenne_transitions': np.mean(entropies_etats_index1_index3)
        }

        return resultats

    def _analyse_multi_echelle_tous_indices(self):
        """
        Analyse multi-échelle pour tous les indices

        Returns:
            dict: Résultats de l'analyse multi-échelle
        """
        import numpy as np

        echelles = [5, 10, 20, 50, 100]  # Tailles de fenêtres
        resultats = {}

        # INDEX5 - utiliser self.sequences directement
        index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
        entropies_par_echelle_index5 = {}

        for echelle in echelles:
            entropies = []
            for i in range(0, len(index5_data), echelle):
                segment = index5_data[i:i+echelle]
                if len(segment) == echelle:
                    h = self.calculer_entropie_shannon(segment)
                    entropies.append(h)

            if entropies:
                entropies_par_echelle_index5[echelle] = {
                    'entropies': entropies,
                    'moyenne': np.mean(entropies),
                    'variance': np.var(entropies),
                    'nb_segments': len(entropies)
                }

        resultats['INDEX5'] = {
            'entropies_par_echelle': entropies_par_echelle_index5,
            'pattern_fractal': self._analyser_pattern_fractal(entropies_par_echelle_index5),
            'echelle_optimale': self._determiner_echelle_optimale(entropies_par_echelle_index5)
        }

        # INDEX2_INDEX3 - utiliser self.sequences directement
        index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
        entropies_par_echelle_index2_index3 = {}

        for echelle in echelles:
            entropies = []
            for i in range(0, len(index2_index3_data), echelle):
                segment = index2_index3_data[i:i+echelle]
                if len(segment) == echelle:
                    h = self.calculer_entropie_shannon(segment)
                    entropies.append(h)

            if entropies:
                entropies_par_echelle_index2_index3[echelle] = {
                    'entropies': entropies,
                    'moyenne': np.mean(entropies),
                    'variance': np.var(entropies),
                    'nb_segments': len(entropies)
                }

        resultats['INDEX2_INDEX3'] = {
            'entropies_par_echelle': entropies_par_echelle_index2_index3,
            'pattern_fractal': self._analyser_pattern_fractal(entropies_par_echelle_index2_index3),
            'echelle_optimale': self._determiner_echelle_optimale(entropies_par_echelle_index2_index3)
        }

        # INDEX1_INDEX3 - utiliser self.sequences directement
        index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
        entropies_par_echelle_index1_index3 = {}

        for echelle in echelles:
            entropies = []
            for i in range(0, len(index1_index3_data), echelle):
                segment = index1_index3_data[i:i+echelle]
                if len(segment) == echelle:
                    h = self.calculer_entropie_shannon(segment)
                    entropies.append(h)

            if entropies:
                entropies_par_echelle_index1_index3[echelle] = {
                    'entropies': entropies,
                    'moyenne': np.mean(entropies),
                    'variance': np.var(entropies),
                    'nb_segments': len(entropies)
                }

        resultats['INDEX1_INDEX3'] = {
            'entropies_par_echelle': entropies_par_echelle_index1_index3,
            'pattern_fractal': self._analyser_pattern_fractal(entropies_par_echelle_index1_index3),
            'echelle_optimale': self._determiner_echelle_optimale(entropies_par_echelle_index1_index3)
        }

        return resultats

    def _analyser_pattern_fractal(self, entropies_par_echelle):
        """
        Analyse le pattern fractal des entropies

        Args:
            entropies_par_echelle: Dictionnaire des entropies par échelle

        Returns:
            str: Type de pattern fractal
        """
        import numpy as np

        if len(entropies_par_echelle) < 2:
            return "INSUFFISANT"

        # Calculer la corrélation entre échelles
        echelles = sorted(entropies_par_echelle.keys())
        moyennes = [entropies_par_echelle[e]['moyenne'] for e in echelles]

        # Calculer la pente de la relation échelle-entropie
        log_echelles = np.log(echelles)
        correlation = np.corrcoef(log_echelles, moyennes)[0, 1]

        if correlation > 0.7:
            return "FRACTAL_POSITIF"
        elif correlation < -0.7:
            return "FRACTAL_NEGATIF"
        else:
            return "NON_FRACTAL"

    def _determiner_echelle_optimale(self, entropies_par_echelle):
        """
        Détermine l'échelle optimale (variance minimale)

        Args:
            entropies_par_echelle: Dictionnaire des entropies par échelle

        Returns:
            int: Échelle optimale
        """
        if not entropies_par_echelle:
            return None

        # Trouver l'échelle avec la variance minimale
        echelle_optimale = min(entropies_par_echelle.keys(),
                              key=lambda e: entropies_par_echelle[e]['variance'])

        return echelle_optimale

    def _entropie_jointe_triple(self, X, Y, Z):
        """
        Calcule l'entropie jointe triple H(X,Y,Z)

        Args:
            X, Y, Z: Variables aléatoires

        Returns:
            float: Entropie jointe triple en bits
        """
        # Créer les triplets (x, y, z)
        triplets = list(zip(X, Y, Z))
        return self.calculer_entropie_shannon(triplets)

    def _analyse_entropie_jointe_complete(self):
        """
        Analyse complète de l'entropie jointe pour tous les indices

        Returns:
            dict: Résultats de l'entropie jointe
        """
        # Extraire les composants depuis INDEX5 pour avoir INDEX1, INDEX2, INDEX3
        composants = self._extraire_composants_index5()
        index1_data = composants['INDEX1']
        index2_data = composants['INDEX2']
        index3_data = composants['INDEX3']

        # Entropies individuelles
        h_index1 = self.calculer_entropie_shannon(index1_data)
        h_index2 = self.calculer_entropie_shannon(index2_data)
        h_index3 = self.calculer_entropie_shannon(index3_data)

        # Entropies jointes par paires
        h_index1_index2 = self._entropie_jointe(index1_data, index2_data)
        h_index1_index3 = self._entropie_jointe(index1_data, index3_data)
        h_index2_index3 = self._entropie_jointe(index2_data, index3_data)

        # Entropie jointe triple
        h_index1_index2_index3 = self._entropie_jointe_triple(index1_data, index2_data, index3_data)

        resultats = {
            'entropies_individuelles': {
                'H_INDEX1': h_index1,
                'H_INDEX2': h_index2,
                'H_INDEX3': h_index3
            },
            'entropies_jointes_paires': {
                'H_INDEX1_INDEX2': h_index1_index2,
                'H_INDEX1_INDEX3': h_index1_index3,
                'H_INDEX2_INDEX3': h_index2_index3
            },
            'entropie_jointe_triple': {
                'H_INDEX1_INDEX2_INDEX3': h_index1_index2_index3
            },
            'informations_mutuelles': {
                'I_INDEX1_INDEX2': h_index1 + h_index2 - h_index1_index2,
                'I_INDEX1_INDEX3': h_index1 + h_index3 - h_index1_index3,
                'I_INDEX2_INDEX3': h_index2 + h_index3 - h_index2_index3
            },
            'information_mutuelle_triple': {
                'I_INDEX1_INDEX2_INDEX3': h_index1 + h_index2 + h_index3 - h_index1_index2_index3
            }
        }

        # Analyse de la structure de dépendance
        somme_individuelles = h_index1 + h_index2 + h_index3
        ratio_compression = h_index1_index2_index3 / somme_individuelles if somme_individuelles > 0 else 1.0

        resultats['analyse_dependance'] = {
            'ratio_compression': ratio_compression,
            'niveau_dependance': self._classifier_niveau_dependance(ratio_compression),
            'redondance_totale': somme_individuelles - h_index1_index2_index3
        }

        return resultats

    def _classifier_niveau_dependance(self, ratio_compression):
        """
        Classifie le niveau de dépendance selon le ratio de compression

        Args:
            ratio_compression: Ratio H(X,Y,Z) / (H(X) + H(Y) + H(Z))

        Returns:
            str: Niveau de dépendance
        """
        if ratio_compression > 0.9:
            return "INDEPENDANCE_FORTE"
        elif ratio_compression > 0.7:
            return "DEPENDANCE_FAIBLE"
        elif ratio_compression > 0.5:
            return "DEPENDANCE_MODEREE"
        else:
            return "DEPENDANCE_FORTE"

    def _calculer_distributions_moyennes_reference(self, parties_reference):
        """
        Calcule les distributions moyennes de référence

        Args:
            parties_reference: Liste de parties de référence

        Returns:
            dict: Distributions moyennes pour chaque indice
        """
        import numpy as np

        distributions = {
            'INDEX5': np.zeros(18),
            'INDEX2_INDEX3': np.zeros(9),
            'INDEX1_INDEX3': np.zeros(6)
        }

        nb_parties = len(parties_reference)

        for partie in parties_reference:
            # INDEX5 - adapter pour utiliser les données directement
            if hasattr(partie, 'sequences'):
                # Si partie est un objet analyseur, utiliser ses séquences
                dist_index5 = self._calculer_distribution(partie.sequences['INDEX5'], 18)
                dist_index2_index3 = self._calculer_distribution(partie.sequences['INDEX2_INDEX3'], 9)
                dist_index1_index3 = self._calculer_distribution(partie.sequences['INDEX1_INDEX3'], 6)
            else:
                # Si partie est un dictionnaire - CORRECTION : utiliser les clés 'parties'
                if 'parties' in partie and len(partie['parties']) > 0:
                    # Prendre la première partie comme référence
                    partie_data = partie['parties'][0]
                    # Créer des distributions uniformes par défaut
                    dist_index5 = [1.0/18] * 18
                    dist_index2_index3 = [1.0/9] * 9
                    dist_index1_index3 = [1.0/6] * 6
                else:
                    # Distributions uniformes par défaut
                    dist_index5 = [1.0/18] * 18
                    dist_index2_index3 = [1.0/9] * 9
                    dist_index1_index3 = [1.0/6] * 6

            distributions['INDEX5'] += np.array(dist_index5)
            distributions['INDEX2_INDEX3'] += np.array(dist_index2_index3)
            distributions['INDEX1_INDEX3'] += np.array(dist_index1_index3)

        # Moyenner
        for indice in distributions:
            distributions[indice] /= nb_parties

        return distributions

    def _calculer_similarite(self, divergence_kl):
        """
        Convertit une divergence KL en score de similarité

        Args:
            divergence_kl: Valeur de divergence KL

        Returns:
            float: Score de similarité entre 0 et 1
        """
        import math

        # Transformation exponentielle décroissante
        return math.exp(-divergence_kl)

    def _calculer_rang_percentile(self, divergence, parties_reference, type_indice):
        """
        Calcule le rang percentile d'une divergence par rapport aux parties de référence

        Args:
            divergence: Divergence de la partie analysée
            parties_reference: Parties de référence
            type_indice: Type d'indice

        Returns:
            float: Rang percentile (0-100)
        """
        import numpy as np

        # Calculer les divergences de toutes les parties de référence
        divergences_reference = []
        distributions_reference = self._calculer_distributions_moyennes_reference(parties_reference)

        for partie_ref in parties_reference:
            if type_indice == 'INDEX5':
                if hasattr(partie_ref, 'sequences'):
                    data = partie_ref.sequences['INDEX5']
                else:
                    # Utiliser distribution uniforme par défaut
                    data = ['SYNC_pair_4_BANKER'] * 18  # Données fictives uniformes
                dist = self._calculer_distribution(data, 18)
                div = self._kl_divergence(dist, distributions_reference['INDEX5'])
            elif type_indice == 'INDEX2_INDEX3':
                if hasattr(partie_ref, 'sequences'):
                    data = partie_ref.sequences['INDEX2_INDEX3']
                else:
                    # Utiliser distribution uniforme par défaut
                    data = ['pair_4_BANKER'] * 9  # Données fictives uniformes
                dist = self._calculer_distribution(data, 9)
                div = self._kl_divergence(dist, distributions_reference['INDEX2_INDEX3'])
            else:  # INDEX1_INDEX3
                if hasattr(partie_ref, 'sequences'):
                    data = partie_ref.sequences['INDEX1_INDEX3']
                else:
                    # Utiliser distribution uniforme par défaut
                    data = ['SYNC_BANKER'] * 6  # Données fictives uniformes
                dist = self._calculer_distribution(data, 6)
                div = self._kl_divergence(dist, distributions_reference['INDEX1_INDEX3'])

            divergences_reference.append(div)

        # Calculer le percentile
        rang = np.sum(np.array(divergences_reference) <= divergence)
        percentile = (rang / len(divergences_reference)) * 100

        return percentile

    def _analyse_entropie_relative_tous_indices(self, parties_reference):
        """
        Analyse de l'entropie relative par rapport à un ensemble de parties de référence

        Args:
            parties_reference: Liste de parties de référence

        Returns:
            dict: Résultats de l'entropie relative
        """
        resultats = {}

        # Calculer les distributions moyennes de référence
        distributions_reference = self._calculer_distributions_moyennes_reference(parties_reference)

        # INDEX5 - utiliser self.sequences directement (seulement si disponible)
        if 'INDEX5' in self.sequences and self.sequences['INDEX5']:
            try:
                index5_data = self._nettoyer_marqueurs(self.sequences['INDEX5'])
                dist_partie_index5 = self._calculer_distribution(index5_data, 18)
                divergence_index5 = self._kl_divergence(dist_partie_index5, distributions_reference['INDEX5'])

                resultats['INDEX5'] = {
                    'divergence_kl_reference': divergence_index5,
                    'similarite': self._calculer_similarite(divergence_index5),
                    'rang_percentile': self._calculer_rang_percentile(divergence_index5, parties_reference, 'INDEX5')
                }
            except Exception as e:
                print(f"      ⚠️ Erreur INDEX5 dans entropie relative : {e}")
                resultats['INDEX5'] = {'erreur': str(e)}

        # INDEX2_INDEX3 - utiliser self.sequences directement (seulement si disponible)
        if 'INDEX2_INDEX3' in self.sequences and self.sequences['INDEX2_INDEX3']:
            try:
                index2_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX2_INDEX3'])
                dist_partie_index2_index3 = self._calculer_distribution(index2_index3_data, 9)
                divergence_index2_index3 = self._kl_divergence(dist_partie_index2_index3, distributions_reference['INDEX2_INDEX3'])

                resultats['INDEX2_INDEX3'] = {
                    'divergence_kl_reference': divergence_index2_index3,
                    'similarite': self._calculer_similarite(divergence_index2_index3),
                    'rang_percentile': self._calculer_rang_percentile(divergence_index2_index3, parties_reference, 'INDEX2_INDEX3')
                }
            except Exception as e:
                print(f"      ⚠️ Erreur INDEX2_INDEX3 dans entropie relative : {e}")
                resultats['INDEX2_INDEX3'] = {'erreur': str(e)}

        # INDEX1_INDEX3 - utiliser self.sequences directement (seulement si disponible)
        if 'INDEX1_INDEX3' in self.sequences and self.sequences['INDEX1_INDEX3']:
            try:
                index1_index3_data = self._nettoyer_marqueurs(self.sequences['INDEX1_INDEX3'])
                dist_partie_index1_index3 = self._calculer_distribution(index1_index3_data, 6)
                divergence_index1_index3 = self._kl_divergence(dist_partie_index1_index3, distributions_reference['INDEX1_INDEX3'])

                resultats['INDEX1_INDEX3'] = {
                    'divergence_kl_reference': divergence_index1_index3,
                    'similarite': self._calculer_similarite(divergence_index1_index3),
                    'rang_percentile': self._calculer_rang_percentile(divergence_index1_index3, parties_reference, 'INDEX1_INDEX3')
                }
            except Exception as e:
                print(f"      ⚠️ Erreur INDEX1_INDEX3 dans entropie relative : {e}")
                resultats['INDEX1_INDEX3'] = {'erreur': str(e)}

        # Score global de singularité (seulement avec les indices disponibles)
        score_singularite = 0.0
        poids_total = 0.0

        if 'INDEX5' in resultats and 'divergence_kl_reference' in resultats['INDEX5']:
            score_singularite += resultats['INDEX5']['divergence_kl_reference'] * 0.5
            poids_total += 0.5

        if 'INDEX2_INDEX3' in resultats and 'divergence_kl_reference' in resultats['INDEX2_INDEX3']:
            score_singularite += resultats['INDEX2_INDEX3']['divergence_kl_reference'] * 0.3
            poids_total += 0.3

        if 'INDEX1_INDEX3' in resultats and 'divergence_kl_reference' in resultats['INDEX1_INDEX3']:
            score_singularite += resultats['INDEX1_INDEX3']['divergence_kl_reference'] * 0.2
            poids_total += 0.2

        # Normaliser le score selon les poids disponibles
        if poids_total > 0:
            score_singularite = score_singularite / poids_total

        resultats['score_singularite_globale'] = score_singularite
        resultats['classification_singularite'] = self._classifier_singularite(score_singularite)

        return resultats

    def _classifier_singularite(self, score_singularite):
        """
        Classifie le niveau de singularité d'une partie

        Args:
            score_singularite: Score global de singularité

        Returns:
            str: Classification de singularité
        """
        if score_singularite < 0.5:
            return "NORMALE"
        elif score_singularite < 1.0:
            return "LEGEREMENT_SINGULIERE"
        elif score_singularite < 2.0:
            return "MODEREMENT_SINGULIERE"
        elif score_singularite < 3.0:
            return "TRES_SINGULIERE"
        else:
            return "EXTREMEMENT_SINGULIERE"

    # ================================================================================
    # 🎯 MÉTHODES D'ANALYSE INDEX2_INDEX3 (9 COMBINAISONS)
    # ================================================================================
    # INDEX2_INDEX3 = pair_4/pair_6/impair_5 + BANKER/PLAYER/TIE
    # Format: "pair_4_BANKER", "impair_5_PLAYER", "pair_6_TIE", etc.
    # ================================================================================

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX2_INDEX3 - TRANSITIONS
    # --------------------------------------------------------------------------------

    def _analyser_transitions_index2_index3(self, sequence: list) -> dict:
        """
        Analyse les transitions entre combinaisons INDEX2_INDEX3 avec formules exactes
        WRAPPER vers lupasco_refactored.statistics.transitions.TransitionAnalyzer.analyser_transitions
        """
        from lupasco_refactored.statistics.transitions import TransitionAnalyzer

        analyzer = TransitionAnalyzer()
        return analyzer.analyser_transitions(sequence, "INDEX2_INDEX3")

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX2_INDEX3 - PATTERNS TEMPORELS
    # --------------------------------------------------------------------------------

    def _analyser_patterns_temporels_index2_index3(self, sequence: list) -> dict:
        """
        Analyse les patterns temporels dans INDEX2_INDEX3 avec formules exactes
        """
        print("   ⏰ Analyse des patterns temporels INDEX2_INDEX3...")

        # 1. Analyse des cycles et périodicités
        cycles = self._detecter_cycles_index2_index3(sequence)

        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_index2_index3(sequence)

        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_index2_index3(sequence)

        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }

    def _detecter_cycles_index2_index3(self, sequence: list, max_periode: int = 50) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Détecte les cycles INDEX2_INDEX3 avec compteurs de frontières précises
        """
        return self._detecter_cycles_avec_frontieres_precises(sequence, 'INDEX2_INDEX3', max_periode)

    def _tester_stationnarite_index2_index3(self, sequence: list, nb_segments: int = 10) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Teste la stationnarité INDEX2_INDEX3 avec compteurs de frontières précises
        """
        return self._tester_stationnarite_avec_frontieres_precises(sequence, 'INDEX2_INDEX3', nb_segments)

    def _analyser_tendances_index2_index3(self, sequence: list) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Analyse les tendances INDEX2_INDEX3 avec compteurs de frontières précises
        """
        return self._analyser_tendances_avec_frontieres_precises(sequence, 'INDEX2_INDEX3')

    def analyser_index2_index3_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX2_INDEX3 avec toutes les formules mathématiques exactes
        pour chacune des 9 combinaisons possibles (INDEX2 + INDEX3)
        WRAPPER vers lupasco_refactored.analyzers.index2_index3_analyzer.Index2Index3Analyzer.analyser_index2_index3_avec_formules_exactes

        Les 9 combinaisons possibles sont :
        - pair_4_BANKER, pair_4_PLAYER, pair_4_TIE
        - pair_6_BANKER, pair_6_PLAYER, pair_6_TIE
        - impair_5_BANKER, impair_5_PLAYER, impair_5_TIE
        """
        from lupasco_refactored.analyzers.index2_index3_analyzer import Index2Index3Analyzer

        analyzer = Index2Index3Analyzer(self.sequences)
        resultats = analyzer.analyser_index2_index3_avec_formules_exactes()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX2_INDEX3_FORMULES_EXACTES'] = resultats

        return resultats

    def analyser_index1_index3_avec_formules_exactes(self):
        """
        Analyse complète de l'INDEX1_INDEX3 avec toutes les formules mathématiques exactes
        pour chacune des 6 combinaisons possibles (INDEX1 + INDEX3)
        WRAPPER vers lupasco_refactored.analyzers.index1_index3_analyzer.Index1Index3Analyzer.analyser_index1_index3_avec_formules_exactes

        Les 6 combinaisons possibles sont :
        - SYNC_BANKER, SYNC_PLAYER, SYNC_TIE
        - DESYNC_BANKER, DESYNC_PLAYER, DESYNC_TIE
        """
        from lupasco_refactored.analyzers.index1_index3_analyzer import Index1Index3Analyzer

        analyzer = Index1Index3Analyzer(self.sequences)
        resultats = analyzer.analyser_index1_index3_avec_formules_exactes()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['INDEX1_INDEX3_FORMULES_EXACTES'] = resultats

        return resultats

    def valider_formules_mathematiques(self):
        """
        🚀 NOUVELLE FONCTION : Valide toutes les formules mathématiques utilisées
        """
        print("\n🔬 VALIDATION DES FORMULES MATHÉMATIQUES")
        print("=" * 45)

        # Validation des formules
        resultats_validation = validate_formulas()

        print("📊 Résultats de validation :")
        for test_name, passed in resultats_validation.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   {test_name}: {status}")

        # Vérifier si toutes les validations ont réussi
        toutes_validees = all(resultats_validation.values())

        if toutes_validees:
            print("\n✅ TOUTES LES FORMULES MATHÉMATIQUES SONT VALIDÉES")
            print("🎯 Les analyses INDEX5, INDEX2_INDEX3, INDEX1_INDEX3 utilisent des formules scientifiquement exactes")
        else:
            print("\n⚠️ CERTAINES FORMULES ONT ÉCHOUÉ À LA VALIDATION")
            print("🔧 Vérification recommandée avant utilisation sur données réelles")

        # Stocker les résultats de validation
        self.resultats['VALIDATION_FORMULES'] = {
            'resultats_tests': resultats_validation,
            'toutes_validees': toutes_validees,
            'nb_tests_reussis': sum(resultats_validation.values()),
            'nb_tests_total': len(resultats_validation)
        }

        return self.resultats['VALIDATION_FORMULES']

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX1_INDEX3 - TRANSITIONS
    # --------------------------------------------------------------------------------

    def _analyser_transitions_index1_index3(self, sequence: list) -> dict:
        """
        Analyse les transitions entre combinaisons INDEX1_INDEX3 avec formules exactes
        """
        print("   🔄 Calcul des matrices de transition INDEX1_INDEX3...")

        # Créer la matrice de transition
        combinaisons = sorted(set(sequence))
        n_combos = len(combinaisons)

        # Matrice de comptage des transitions
        matrice_transitions = np.zeros((n_combos, n_combos))

        for i in range(len(sequence) - 1):
            from_idx = combinaisons.index(sequence[i])
            to_idx = combinaisons.index(sequence[i + 1])
            matrice_transitions[from_idx, to_idx] += 1

        # Convertir en probabilités
        matrice_probabilities = np.zeros_like(matrice_transitions)
        for i in range(n_combos):
            row_sum = np.sum(matrice_transitions[i, :])
            if row_sum > 0:
                matrice_probabilities[i, :] = matrice_transitions[i, :] / row_sum

        # Calculer l'entropie de chaque ligne (diversité des transitions)
        entropies_transitions = []
        for i in range(n_combos):
            probs = matrice_probabilities[i, :]
            probs = probs[probs > 0]  # Supprimer les zéros
            if len(probs) > 0:
                entropie = -np.sum(probs * np.log2(probs))
                entropies_transitions.append(entropie)
            else:
                entropies_transitions.append(0)

        # Test d'indépendance chi-carré avec vérification de sécurité
        try:
            chi2_stat, p_value, dof, expected = chi2_contingency(matrice_transitions)
        except ValueError as e:
            print(f"      ⚠️ Chi2 test échoué : {e}")
            chi2_stat, p_value, dof = 0, 1.0, 0

        # Coefficient de Gini pour mesurer la concentration des transitions
        gini_transitions = []
        for i in range(n_combos):
            row = matrice_transitions[i, :]
            if np.sum(row) > 0:
                gini_transitions.append(gini_coefficient(row))
            else:
                gini_transitions.append(0)

        return {
            'matrice_transitions': matrice_transitions.tolist(),
            'matrice_probabilities': matrice_probabilities.tolist(),
            'entropies_transitions': entropies_transitions,
            'gini_transitions': gini_transitions,
            'test_independance': {
                'chi2_statistic': chi2_stat,
                'p_value': p_value,
                'degrees_freedom': dof,
                'independant': p_value > 0.05
            },
            'combinaisons_ordre': combinaisons
        }

    # --------------------------------------------------------------------------------
    # 🔧 MÉTHODES DE SUPPORT INDEX1_INDEX3 - PATTERNS TEMPORELS
    # --------------------------------------------------------------------------------

    def _analyser_patterns_temporels_index1_index3(self, sequence: list) -> dict:
        """
        Analyse les patterns temporels dans INDEX1_INDEX3 avec formules exactes
        """
        print("   ⏰ Analyse des patterns temporels INDEX1_INDEX3...")

        # 1. Analyse des cycles et périodicités
        cycles = self._detecter_cycles_index1_index3(sequence)

        # 2. Analyse de la stationnarité (test de runs sur des segments)
        stationnarite = self._tester_stationnarite_index1_index3(sequence)

        # 3. Analyse des tendances temporelles
        tendances = self._analyser_tendances_index1_index3(sequence)

        return {
            'cycles': cycles,
            'stationnarite': stationnarite,
            'tendances': tendances
        }

    def _detecter_cycles_index1_index3(self, sequence: list, max_periode: int = 50) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Détecte les cycles INDEX1_INDEX3 avec compteurs de frontières précises
        """
        return self._detecter_cycles_avec_frontieres_precises(sequence, 'INDEX1_INDEX3', max_periode)

    def _tester_stationnarite_index1_index3(self, sequence: list, nb_segments: int = 10) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Teste la stationnarité INDEX1_INDEX3 avec compteurs de frontières précises
        """
        return self._tester_stationnarite_avec_frontieres_precises(sequence, 'INDEX1_INDEX3', nb_segments)

    def _analyser_tendances_index1_index3(self, sequence: list) -> dict:
        """
        🔧 SOLUTION APPLIQUÉE : Analyse les tendances INDEX1_INDEX3 avec compteurs de frontières précises
        """
        return self._analyser_tendances_avec_frontieres_precises(sequence, 'INDEX1_INDEX3')

    def analyser_entropie_avancee_tous_indices(self):
        """
        MÉTHODE PRINCIPALE - Analyse entropique avancée complète pour tous les indices
        WRAPPER vers lupasco_refactored.statistics.entropy_advanced.AdvancedEntropy.analyser_entropie_avancee_complete

        Intègre toutes les 10 équations d'entropie avec les adaptations nécessaires

        Returns:
            dict: Résultats complets de l'analyse entropique avancée
        """
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy

        # Extraire les composants INDEX5
        composants_index5 = self._extraire_composants_index5()

        # Créer l'analyseur d'entropie avancée
        analyzer = AdvancedEntropy()

        # Analyser avec le nouveau module
        return analyzer.analyser_entropie_avancee_complete(self.sequences, composants_index5)



        # Score de prédictibilité (0 = parfait, 1 = aléatoire)
        predictability_score = h_conditional / h_index5 if h_index5 > 0 else 1

        print(f"\n📈 MÉTRIQUES DE PRÉDICTIBILITÉ :")
        print(f"   Entropie H(INDEX5) : {h_index5:.6f} bits")
        print(f"   Entropie conditionnelle H(INDEX5|INDEX1) : {h_conditional:.6f} bits")
        print(f"   Information mutuelle I(INDEX1;INDEX5) : {mutual_info:.6f} bits")
        print(f"   Score de prédictibilité : {predictability_score:.6f}")
        print(f"   Réduction d'incertitude : {(1-predictability_score)*100:.2f}%")

        if predictability_score < 0.9:
            print(f"   ✅ INDEX5 est PRÉDICTIBLE par INDEX1")
        else:
            print(f"   ❌ INDEX5 est PEU PRÉDICTIBLE par INDEX1")

        # Stocker les résultats
        self.resultats['PREDICTIBILITE_INDEX5_PAR_INDEX1'] = {
            'probabilites_conditionnelles': prob_conditionnelles,
            'entropie_index5': h_index5,
            'entropie_conditionnelle': h_conditional,
            'information_mutuelle': mutual_info,
            'score_predictibilite': predictability_score,
            'reduction_incertitude_pct': (1-predictability_score)*100,
            'predictible': predictability_score < 0.9
        }

        return self.resultats['PREDICTIBILITE_INDEX5_PAR_INDEX1']

    def _entropie_conditionnelle(self, y_data, x_data):
        """
        Calcule l'entropie conditionnelle H(Y|X)
        🔧 CORRECTION : Bug dans le calcul des occurrences conjointes
        """
        from collections import Counter
        import math

        if not y_data or not x_data or len(y_data) != len(x_data):
            return 0.0

        # 🔧 CORRECTION : Calcul direct des occurrences par valeur de X
        x_counts = Counter(x_data)
        total = len(y_data)
        h_conditional = 0.0

        print(f"🔍 DEBUG Entropie conditionnelle:")
        print(f"   Total éléments: {total}")
        print(f"   Valeurs X uniques: {list(x_counts.keys())}")

        # Pour chaque valeur de X
        for x_val in x_counts:
            # 🔧 CORRECTION : Filtrer directement Y pour cette valeur de X
            y_given_x = [y_data[i] for i in range(len(y_data)) if x_data[i] == x_val]

            if y_given_x:
                p_x = len(y_given_x) / total
                y_counts = Counter(y_given_x)
                total_y_given_x = len(y_given_x)

                # Calculer H(Y|X=x_val)
                h_y_given_x = 0.0
                for y_val, count in y_counts.items():
                    if count > 0:
                        p_y_given_x = count / total_y_given_x
                        h_y_given_x -= p_y_given_x * math.log2(p_y_given_x)

                h_conditional += p_x * h_y_given_x

                print(f"   X={x_val}: P(X)={p_x:.4f}, H(Y|X={x_val})={h_y_given_x:.6f}")

        print(f"   H(Y|X) final = {h_conditional:.6f}")
        return h_conditional

    def _information_mutuelle(self, x_data, y_data):
        """
        Calcule l'information mutuelle I(X;Y) = H(Y) - H(Y|X)
        🔧 CORRECTION : Vérification des valeurs négatives impossibles
        """
        from formules_mathematiques_exactes import shannon_entropy_from_data

        if not x_data or not y_data or len(x_data) != len(y_data):
            return 0.0

        h_y = shannon_entropy_from_data(y_data)
        h_y_given_x = self._entropie_conditionnelle(y_data, x_data)

        # 🔧 CORRECTION : L'information mutuelle ne peut pas être négative
        mutual_info = h_y - h_y_given_x

        # Debug pour identifier le problème
        print(f"🔍 DEBUG Information mutuelle:")
        print(f"   H(Y) = {h_y:.6f}")
        print(f"   H(Y|X) = {h_y_given_x:.6f}")
        print(f"   I(X;Y) = {mutual_info:.6f}")

        # Forcer à 0 si négatif (erreur numérique)
        if mutual_info < 0:
            print(f"⚠️ Information mutuelle négative détectée, forcée à 0")
            mutual_info = 0.0

        return mutual_info

    def _calculer_probabilites_conditionnelles_partie(self, index1_partie, index3_partie):
        """
        Calcule les probabilités conditionnelles P(INDEX3|INDEX1) pour une partie
        """
        from collections import Counter

        if len(index1_partie) != len(index3_partie) or len(index1_partie) < 5:
            return None

        prob_conditionnelles = {}

        # Pour chaque valeur d'INDEX1 dans cette partie
        for sync_state in ['SYNC', 'DESYNC']:
            # Filtrer les données pour cette valeur d'INDEX1
            indices_sync = [i for i, val in enumerate(index1_partie) if val == sync_state]

            if len(indices_sync) < 2:  # Pas assez de données
                continue

            index3_given_sync = [index3_partie[i] for i in indices_sync]

            # Compter les occurrences d'INDEX3 pour cette valeur d'INDEX1
            compteur = Counter(index3_given_sync)
            total = len(index3_given_sync)

            prob_conditionnelles[sync_state] = {}
            for result, count in compteur.items():
                prob_conditionnelles[sync_state][result] = count / total

        return prob_conditionnelles

    def analyser_lupasco_par_parties(self, nb_parties_echantillon=100):
        """
        🔧 NOUVELLE MÉTHODE : Analyse Lupasco sur un échantillon de parties indépendantes
        """
        print(f"\n🔬 ANALYSE LUPASCO PAR PARTIES (échantillon de {nb_parties_echantillon} parties)")
        print("=" * 70)

        # Diviser INDEX5 en parties
        index5_brute = self.sequences['INDEX5']
        parties_index5 = self._diviser_en_parties(index5_brute)

        analyses_parties = []

        # Analyser un échantillon de parties
        step = max(1, len(parties_index5) // nb_parties_echantillon)
        parties_echantillon = parties_index5[::step][:nb_parties_echantillon]

        print(f"📊 Analyse de {len(parties_echantillon)} parties (sur {len(parties_index5)} total)")

        for i, partie_seq in enumerate(parties_echantillon):
            if len(partie_seq) < 20:  # Ignorer parties trop petites
                continue

            # Extraire INDEX1, INDEX2, INDEX3 pour cette partie
            index1_partie = []
            index2_partie = []
            index3_partie = []

            for combinaison in partie_seq:
                parts = combinaison.split('_')
                if len(parts) == 4:  # Format: SYNC_pair_4_BANKER
                    index1_partie.append(parts[0])  # SYNC/DESYNC
                    index2_partie.append(f"{parts[1]}_{parts[2]}")  # pair_4
                    index3_partie.append(parts[3])  # BANKER

            if len(index1_partie) >= 20:  # Assez de données pour analyse
                try:
                    analyse_partie = lupasco_entropy_analysis(index1_partie, index2_partie, index3_partie)
                    analyses_parties.append({
                        'partie_id': i,
                        'taille': len(partie_seq),
                        'entropies': {
                            'index1': analyse_partie['entropy_index1'],
                            'index2': analyse_partie['entropy_index2'],
                            'index3': analyse_partie['entropy_index3']
                        },
                        'informations_mutuelles': {
                            'i12': analyse_partie['mutual_info_12'],
                            'i13': analyse_partie['mutual_info_13'],
                            'i23': analyse_partie['mutual_info_23']
                        }
                    })
                except Exception as e:
                    print(f"   ⚠️ Erreur partie {i}: {e}")

        # Calculer les statistiques sur les parties
        if analyses_parties:
            print(f"\n📈 RÉSULTATS SUR {len(analyses_parties)} PARTIES ANALYSÉES :")

            # Moyennes des entropies
            h1_moyen = sum(p['entropies']['index1'] for p in analyses_parties) / len(analyses_parties)
            h2_moyen = sum(p['entropies']['index2'] for p in analyses_parties) / len(analyses_parties)
            h3_moyen = sum(p['entropies']['index3'] for p in analyses_parties) / len(analyses_parties)

            # Moyennes des informations mutuelles
            i12_moyen = sum(p['informations_mutuelles']['i12'] for p in analyses_parties) / len(analyses_parties)
            i13_moyen = sum(p['informations_mutuelles']['i13'] for p in analyses_parties) / len(analyses_parties)
            i23_moyen = sum(p['informations_mutuelles']['i23'] for p in analyses_parties) / len(analyses_parties)

            print(f"   Entropie INDEX1 moyenne : {h1_moyen:.6f} bits")
            print(f"   Entropie INDEX2 moyenne : {h2_moyen:.6f} bits")
            print(f"   Entropie INDEX3 moyenne : {h3_moyen:.6f} bits")
            print(f"   Information mutuelle I(INDEX1;INDEX2) moyenne : {i12_moyen:.6f} bits")
            print(f"   Information mutuelle I(INDEX1;INDEX3) moyenne : {i13_moyen:.6f} bits")
            print(f"   Information mutuelle I(INDEX2;INDEX3) moyenne : {i23_moyen:.6f} bits")

            # Stocker les résultats
            self.resultats['LUPASCO_PAR_PARTIES'] = {
                'nb_parties_analysees': len(analyses_parties),
                'entropies_moyennes': {
                    'index1': h1_moyen,
                    'index2': h2_moyen,
                    'index3': h3_moyen
                },
                'informations_mutuelles_moyennes': {
                    'i12': i12_moyen,
                    'i13': i13_moyen,
                    'i23': i23_moyen
                },
                'analyses_detaillees': analyses_parties
            }

            return self.resultats['LUPASCO_PAR_PARTIES']
        else:
            print("❌ Aucune partie analysable trouvée")
            return {}

    # ================================================================================
    # 🎯 MÉTHODES GÉNÉRALES ET UTILITAIRES
    # ================================================================================
    # Méthodes communes utilisées par tous les indices
    # ================================================================================

    def analyser_toutes_sequences(self):
        """Lance l'analyse complète de toutes les séquences"""
        print("\nLANCEMENT DE L'ANALYSE COMPLÈTE")
        print("=" * 50)

        # 🚀 NOUVELLE PHASE : Validation des formules mathématiques
        print("\nPHASE 0: VALIDATION DES FORMULES MATHÉMATIQUES")
        print("-" * 55)

        self.valider_formules_mathematiques()

        # 🎯 ANALYSES SPÉCIALISÉES DEMANDÉES
        print("\nPHASE SPÉCIALISÉE: ANALYSES AVANCÉES DEMANDÉES")
        print("-" * 55)

        self.analyser_transitions_desync_sync()
        self.analyser_cycles_periode_2_et_3()
        self.analyser_predictibilite_index3_par_index1()
        self.analyser_predictibilite_index5_par_index1()

        # 🔧 NOUVELLE ANALYSE : Lupasco par parties avec vraies données
        self.analyser_lupasco_par_parties(nb_parties_echantillon=50)

        # 1. Analyse des runs (analyse originale)
        print("\nPHASE 1: ANALYSE DES RUNS")
        print("-" * 30)

        for nom_index, sequence in self.sequences.items():
            if nom_index in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:  # Analyser INDEX principaux + INDEX5
                print(f"\nAnalyse des runs {nom_index}")

                if nom_index == 'INDEX5':
                    print(f"   INDEX5 : Analyse des 18 combinaisons possibles")
                    # CORRECTION CRITIQUE : Nettoyer la séquence AVANT l'analyse
                    sequence_propre = [x for x in sequence if x != "__FIN_PARTIE__"]
                    combinaisons_uniques = sorted(set(sequence_propre))
                    print(f"   Combinaisons trouvées : {len(combinaisons_uniques)}")
                    for i, combo in enumerate(combinaisons_uniques, 1):
                        count = sequence_propre.count(combo)
                        pourcentage = (count / len(sequence_propre)) * 100
                        print(f"      {i:2d}. {combo} : {count:,} fois ({pourcentage:.2f}%)")

                # Analyse des runs
                resultats_runs = self.analyser_runs(sequence, nom_index)

                # Autocorrélation
                print(f"Calcul de l'autocorrélation...")
                autocorr = self.calculer_autocorrelation(sequence)

                # Entropie de Shannon
                print(f"Calcul de l'entropie de Shannon...")
                entropie = self.calculer_entropie_shannon(sequence)

                # Stocker les résultats
                self.resultats[nom_index] = {
                    'runs': resultats_runs,
                    'autocorrelation': autocorr,
                    'entropie_shannon': entropie,
                    'taille_sequence': len(sequence)
                }

                print(f"Analyse {nom_index} terminée")

        # 2. Analyse INDEX5 avec formules exactes
        print("\nPHASE 2: ANALYSE INDEX5 AVEC FORMULES EXACTES")
        print("-" * 50)

        self.analyser_index5_avec_formules_exactes()

        # 3. Analyse INDEX2_INDEX3 avec formules exactes
        print("\nPHASE 3: ANALYSE INDEX2_INDEX3 AVEC FORMULES EXACTES")
        print("-" * 55)

        self.analyser_index2_index3_avec_formules_exactes()

        # 4. Analyse INDEX1_INDEX3 avec formules exactes
        print("\nPHASE 4: ANALYSE INDEX1_INDEX3 AVEC FORMULES EXACTES")
        print("-" * 55)

        self.analyser_index1_index3_avec_formules_exactes()

        # 5. Analyses statistiques avancées avec nouvelles méthodes
        print("\nPHASE 5: ANALYSES STATISTIQUES AVANCÉES")
        print("-" * 45)

        self._analyses_avancees = {}

        # Analyser INDEX5 avec les nouvelles méthodes
        if 'INDEX5' in self.sequences:
            print("Analyse statistique avancée INDEX5...")
            self._analyses_avancees['INDEX5'] = self.analyser_statistiques_avancees(
                self.sequences['INDEX5'], 'INDEX5'
            )

        # Analyser INDEX2_INDEX3 avec les nouvelles méthodes
        if 'INDEX2_INDEX3' in self.sequences:
            print("Analyse statistique avancée INDEX2_INDEX3...")
            self._analyses_avancees['INDEX2_INDEX3'] = self.analyser_statistiques_avancees(
                self.sequences['INDEX2_INDEX3'], 'INDEX2_INDEX3'
            )

        # Analyser INDEX1_INDEX3 avec les nouvelles méthodes
        if 'INDEX1_INDEX3' in self.sequences:
            print("Analyse statistique avancée INDEX1_INDEX3...")
            self._analyses_avancees['INDEX1_INDEX3'] = self.analyser_statistiques_avancees(
                self.sequences['INDEX1_INDEX3'], 'INDEX1_INDEX3'
            )

        print("\nAnalyse complète terminée")

    def generer_rapport(self, fichier_sortie: str = None):
        """
        Génère un rapport détaillé des analyses
        WRAPPER vers lupasco_refactored.reporting.report_generator.ReportGenerator.generer_rapport

        Args:
            fichier_sortie: Nom du fichier de rapport (optionnel)
        """
        from lupasco_refactored.reporting.report_generator import ReportGenerator

        # Créer le générateur de rapports
        generator = ReportGenerator(
            self.resultats,
            self.sequences,
            self.fichier_json,
            self.nb_parties_total
        )

        # Générer le rapport avec le nouveau module
        return generator.generer_rapport(fichier_sortie)




    # ================================================================================
    # 🎯 MÉTHODES LUPASCO SPÉCIALISÉES (WRAPPERS)
    # ================================================================================
    # Wrappers vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer
    # ================================================================================

    def analyser_transitions_desync_sync(self):
        """
        Analyse les transitions DESYNC→SYNC et SYNC→DESYNC dans INDEX1
        WRAPPER vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer.analyser_transitions_desync_sync
        """
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer

        analyzer = LupascoAnalyzer(self.sequences)
        resultats = analyzer.analyser_transitions_desync_sync()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['TRANSITIONS_DESYNC_SYNC'] = resultats

        return resultats

    def analyser_cycles_periode_2_et_3(self):
        """
        Analyse les cycles de période 2 et 3 dans toutes les séquences
        WRAPPER vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer.analyser_cycles_periode_2_et_3
        """
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer

        analyzer = LupascoAnalyzer(self.sequences)
        resultats = analyzer.analyser_cycles_periode_2_et_3()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['CYCLES_PERIODE_2_ET_3'] = resultats

        return resultats

    def analyser_predictibilite_index3_par_index1(self):
        """
        Analyse la prédictibilité d'INDEX3 par INDEX1
        WRAPPER vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer.analyser_predictibilite_index3_par_index1
        """
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer

        analyzer = LupascoAnalyzer(self.sequences)
        resultats = analyzer.analyser_predictibilite_index3_par_index1()

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['PREDICTIBILITE_INDEX3_PAR_INDEX1'] = resultats

        return resultats

    def analyser_lupasco_par_parties(self, nb_parties_echantillon: int = 100):
        """
        Analyse Lupasco sur un échantillon de parties indépendantes
        WRAPPER vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer.analyser_lupasco_par_parties

        Args:
            nb_parties_echantillon: Nombre de parties à analyser
        """
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer

        analyzer = LupascoAnalyzer(self.sequences)
        resultats = analyzer.analyser_lupasco_par_parties(nb_parties_echantillon)

        # Stocker les résultats dans self.resultats pour compatibilité
        self.resultats['LUPASCO_PAR_PARTIES'] = resultats

        return resultats

    def analyser_predictibilite_index5_par_index1(self):
        """
        Analyse la prédictibilité d'INDEX5 par INDEX1
        WRAPPER vers lupasco_refactored.analyzers.lupasco_analyzer.LupascoAnalyzer.analyser_predictibilite_index3_par_index1
        (utilise INDEX3 comme proxy pour INDEX5)
        """
        # Vérifier si l'analyse INDEX3 par INDEX1 a déjà été faite
        if 'PREDICTIBILITE_INDEX3_PAR_INDEX1' in self.resultats:
            # Réutiliser les résultats existants pour éviter la duplication
            resultats = self.resultats['PREDICTIBILITE_INDEX3_PAR_INDEX1'].copy()
            resultats['note'] = 'Analyse INDEX5 par INDEX1 utilise la même logique que INDEX3 par INDEX1 (résultats identiques)'

            # Stocker les résultats dans self.resultats pour compatibilité
            self.resultats['PREDICTIBILITE_INDEX5_PAR_INDEX1'] = resultats

            print("🔄 Réutilisation des résultats INDEX3 par INDEX1 pour INDEX5 (évite duplication)")
            return resultats
        else:
            # Si pas encore fait, faire l'analyse normale
            from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer

            analyzer = LupascoAnalyzer(self.sequences)

            # Utiliser INDEX3 comme proxy pour INDEX5 (même logique)
            resultats = analyzer.analyser_predictibilite_index3_par_index1()

            # Adapter les résultats pour INDEX5
            if 'erreur' not in resultats:
                resultats['note'] = 'Analyse INDEX5 par INDEX1 utilise la même logique que INDEX3 par INDEX1'

            # Stocker les résultats dans self.resultats pour compatibilité
            self.resultats['PREDICTIBILITE_INDEX5_PAR_INDEX1'] = resultats

            return resultats


def trouver_fichier_json_recent():
    """
    Trouve automatiquement le fichier JSON le plus récent dans le dossier du script

    Returns:
        str: Chemin vers le fichier JSON le plus récent

    Raises:
        FileNotFoundError: Si aucun fichier JSON n'est trouvé
    """
    import glob
    import os
    from datetime import datetime

    # Obtenir le répertoire où se trouve le script analyseur.py
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Chercher tous les fichiers .json dans le dossier du script
    pattern = os.path.join(script_dir, "*.json")
    fichiers_json = glob.glob(pattern)

    if not fichiers_json:
        raise FileNotFoundError(f"Aucun fichier JSON trouvé dans le dossier : {script_dir}")

    # Trier par date de modification (le plus récent en premier)
    fichiers_json.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    fichier_recent = fichiers_json[0]
    # Extraire seulement le nom du fichier pour l'affichage
    nom_fichier = os.path.basename(fichier_recent)
    taille_mb = os.path.getsize(fichier_recent) / (1024 * 1024)
    date_modif = os.path.getmtime(fichier_recent)

    print(f"Dossier de recherche : {script_dir}")
    print(f"Fichier JSON le plus récent trouvé : {nom_fichier}")
    print(f"Taille : {taille_mb:.1f} MB")
    print(f"Date de modification : {datetime.fromtimestamp(date_modif).strftime('%Y-%m-%d %H:%M:%S')}")

    return fichier_recent


def main():
    """Fonction principale du programme"""
    import sys
    import os

    print("ANALYSEUR STATISTIQUE DES SEQUENCES LUPASCO - VERSION SIMPLIFIEE")
    print("=" * 70)
    print("Analyse des runs, entropie, autocorrelation et generation de rapport")
    print()

    # Déterminer le fichier JSON à utiliser
    if len(sys.argv) >= 2:
        # Fichier spécifié en argument
        fichier_json = sys.argv[1]
        print(f"Fichier JSON spécifié : {fichier_json}")
    else:
        # Recherche automatique du fichier le plus récent
        print("Recherche automatique du fichier JSON le plus récent...")
        try:
            fichier_json = trouver_fichier_json_recent()
        except FileNotFoundError as e:
            print(f"ERREUR : {e}")
            print("\nOptions :")
            print("1. Placez un fichier JSON dans le dossier courant")
            print("2. Spécifiez le fichier en argument :")
            print("   python analyseur.py <fichier_json>")
            sys.exit(1)

    # Vérifier que le fichier existe
    if not os.path.exists(fichier_json):
        print(f"ERREUR : Le fichier {fichier_json} n'existe pas")
        sys.exit(1)

    print()

    try:
        # Créer l'analyseur
        analyseur = AnalyseurSequencesLupasco(fichier_json)

        # Charger les données
        analyseur.charger_donnees()

        # Lancer l'analyse complète
        analyseur.analyser_toutes_sequences()

        # Générer le rapport
        fichier_rapport = analyseur.generer_rapport()

        print(f"\nANALYSE TERMINEE AVEC SUCCES !")
        print(f"Rapport détaillé : {fichier_rapport}")

    except Exception as e:
        print(f"\nERREUR : {e}")
        sys.exit(1)





if __name__ == "__main__":
    main()
