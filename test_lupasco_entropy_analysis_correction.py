#!/usr/bin/env python3
"""
TEST SPÉCIFIQUE POUR LA CORRECTION DE LUPASCO_ENTROPY_ANALYSIS
==============================================================

Ce script teste la correction de l'erreur "unsupported operand type(s) for +: 'int' and 'dict'"
dans la fonction lupasco_entropy_analysis de formules_mathematiques_exactes.py

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_lupasco_entropy_analysis_correction():
    """
    Test spécifique pour vérifier que l'erreur dans lupasco_entropy_analysis est corrigée
    """
    print("🔬 TEST SPÉCIFIQUE - CORRECTION LUPASCO_ENTROPY_ANALYSIS")
    print("=" * 60)
    
    try:
        # 1. TESTER LA FONCTION LUPASCO_ENTROPY_ANALYSIS DIRECTEMENT
        print("\n📊 PHASE 1: TEST DIRECT DE LUPASCO_ENTROPY_ANALYSIS")
        print("-" * 55)
        
        from formules_mathematiques_exactes import lupasco_entropy_analysis
        
        # Créer des données de test
        np.random.seed(42)
        index1_test = np.random.choice(['SYNC', 'DESYNC'], 100).tolist()
        index2_test = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 100).tolist()
        index3_test = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 100).tolist()
        
        print("   🔄 Test avec données normales...")
        resultats_normaux = lupasco_entropy_analysis(index1_test, index2_test, index3_test)
        
        print(f"   ✅ Analyse normale réussie")
        
        # Vérifier la structure des résultats
        if 'complexity_metrics' in resultats_normaux:
            complexity = resultats_normaux['complexity_metrics']
            print(f"   ✅ Métriques de complexité présentes")
            
            # Vérifier que toutes les valeurs sont numériques
            for key, value in complexity.items():
                if key != 'erreur' and not isinstance(value, (int, float)):
                    print(f"   ❌ Valeur non numérique détectée: {key} = {type(value)}")
                    return False
                elif key != 'erreur':
                    print(f"      {key}: {value:.6f}")
        else:
            print(f"   ❌ Métriques de complexité manquantes")
            return False
        
        # 2. TESTER AVEC DES DONNÉES PROBLÉMATIQUES
        print("\n🧪 PHASE 2: TEST AVEC DONNÉES PROBLÉMATIQUES")
        print("-" * 50)
        
        # Test avec données vides
        print("   🔄 Test avec données vides...")
        try:
            resultats_vides = lupasco_entropy_analysis([], [], [])
            print(f"   ✅ Données vides gérées correctement")
        except Exception as e:
            print(f"   ❌ Erreur avec données vides : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE AVEC DONNÉES VIDES !")
                return False
        
        # Test avec données de longueurs différentes
        print("   🔄 Test avec données de longueurs différentes...")
        try:
            resultats_diff = lupasco_entropy_analysis(
                ['SYNC'] * 10,
                ['pair_4'] * 5,
                ['PLAYER'] * 15
            )
            print(f"   ✅ Données de longueurs différentes gérées")
        except Exception as e:
            print(f"   ❌ Erreur avec longueurs différentes : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE AVEC LONGUEURS DIFFÉRENTES !")
                return False
        
        # Test avec données constantes
        print("   🔄 Test avec données constantes...")
        try:
            resultats_const = lupasco_entropy_analysis(
                ['SYNC'] * 100,
                ['pair_4'] * 100,
                ['PLAYER'] * 100
            )
            print(f"   ✅ Données constantes gérées correctement")
            
            # Vérifier les métriques de complexité
            if 'complexity_metrics' in resultats_const:
                complexity_const = resultats_const['complexity_metrics']
                for key, value in complexity_const.items():
                    if key != 'erreur' and not isinstance(value, (int, float)):
                        print(f"   ❌ Valeur non numérique avec données constantes: {key} = {type(value)}")
                        return False
            
        except Exception as e:
            print(f"   ❌ Erreur avec données constantes : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE AVEC DONNÉES CONSTANTES !")
                return False
        
        # 3. TESTER LES FONCTIONS SÉCURISÉES
        print("\n🔧 PHASE 3: TEST DES FONCTIONS SÉCURISÉES")
        print("-" * 45)
        
        # Simuler des valeurs problématiques pour tester les fonctions sécurisées
        print("   🔄 Test des fonctions safe_add et safe_subtract...")
        
        # Ces fonctions sont définies localement dans lupasco_entropy_analysis
        # On va tester indirectement en forçant des conditions d'erreur
        
        # Test avec des données qui pourraient causer des problèmes d'entropie
        try:
            # Données avec une seule valeur unique (entropie = 0)
            resultats_unique = lupasco_entropy_analysis(
                ['SYNC'] * 50,
                ['pair_4'] * 50,
                ['PLAYER'] * 50
            )
            
            if 'complexity_metrics' in resultats_unique:
                complexity_unique = resultats_unique['complexity_metrics']
                print(f"   ✅ Fonctions sécurisées fonctionnent avec données uniques")
                
                # Afficher quelques métriques pour vérification
                for key in ['total_entropy', 'total_mutual_info']:
                    if key in complexity_unique:
                        value = complexity_unique[key]
                        if isinstance(value, (int, float)):
                            print(f"      {key}: {value:.6f}")
                        else:
                            print(f"   ❌ Valeur non numérique: {key} = {type(value)}")
                            return False
            
        except Exception as e:
            print(f"   ❌ Erreur avec fonctions sécurisées : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS FONCTIONS SÉCURISÉES !")
                return False
        
        # 4. VALIDATION FINALE
        print("\n🏆 PHASE 4: VALIDATION FINALE")
        print("-" * 30)
        
        # Test avec un grand volume de données
        print("   🔄 Test avec grand volume de données...")
        try:
            np.random.seed(123)
            large_index1 = np.random.choice(['SYNC', 'DESYNC'], 1000).tolist()
            large_index2 = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 1000).tolist()
            large_index3 = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 1000).tolist()
            
            resultats_large = lupasco_entropy_analysis(large_index1, large_index2, large_index3)
            
            print(f"   ✅ Grand volume de données traité correctement")
            
            # Vérification finale des types
            if 'complexity_metrics' in resultats_large:
                complexity_large = resultats_large['complexity_metrics']
                all_numeric = all(
                    isinstance(v, (int, float)) for k, v in complexity_large.items() 
                    if k != 'erreur'
                )
                
                if all_numeric:
                    print(f"   ✅ Toutes les métriques sont numériques")
                else:
                    print(f"   ❌ Certaines métriques ne sont pas numériques")
                    return False
            
        except Exception as e:
            print(f"   ❌ Erreur avec grand volume : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE AVEC GRAND VOLUME !")
                return False
        
        # 5. RÉSUMÉ FINAL
        print("\n🎉 PHASE 5: RÉSUMÉ FINAL")
        print("-" * 25)
        
        print("   ✅ lupasco_entropy_analysis corrigée")
        print("   ✅ Fonctions sécurisées implémentées")
        print("   ✅ Validation des types ajoutée")
        print("   ✅ Gestion d'erreurs robuste")
        print("   ✅ Pas d'erreur 'int' + 'dict' détectée")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR DANS LE TEST : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST LUPASCO_ENTROPY_ANALYSIS")
    print("=" * 50)
    
    succes = test_lupasco_entropy_analysis_correction()
    
    print("\n" + "=" * 50)
    if succes:
        print("✅ TEST RÉUSSI - LUPASCO_ENTROPY_ANALYSIS CORRIGÉE")
        print("La fonction gère maintenant correctement tous les types.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ - PROBLÈME DANS LUPASCO_ENTROPY_ANALYSIS")
        print("Des corrections supplémentaires sont nécessaires.")
        sys.exit(1)
