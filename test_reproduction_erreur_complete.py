#!/usr/bin/env python3
"""
TEST DE REPRODUCTION COMPLÈTE DE L'ERREUR
==========================================

Ce script reproduit exactement l'erreur mentionnée :
✅ TOUTES LES FORMULES MATHÉMATIQUES SONT VALIDÉES
🎯 Les analyses INDEX5, INDEX2_INDEX3, INDEX1_INDEX3 utilisent des formules scientifiquement exactes
ERREUR : unsupported operand type(s) for +: 'int' and 'dict'

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np
import traceback

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_reproduction_erreur_complete():
    """
    Reproduit exactement l'erreur mentionnée
    """
    print("🔬 REPRODUCTION COMPLÈTE DE L'ERREUR")
    print("=" * 40)
    
    try:
        # 1. VALIDATION DES FORMULES MATHÉMATIQUES
        print("\n✅ TOUTES LES FORMULES MATHÉMATIQUES SONT VALIDÉES")
        
        from formules_mathematiques_exactes import validate_formulas
        validation_result = validate_formulas()
        
        if validation_result:
            print("🎯 Les analyses INDEX5, INDEX2_INDEX3, INDEX1_INDEX3 utilisent des formules scientifiquement exactes")
        
        # 2. TESTER LES ANALYSES SPÉCIFIQUES
        print("\n📊 PHASE 2: TEST DES ANALYSES SPÉCIFIQUES")
        print("-" * 45)
        
        # Créer des données de test réalistes
        np.random.seed(42)
        test_sequences = {
            'INDEX1': np.random.choice(['SYNC', 'DESYNC'], 100).tolist(),
            'INDEX2': np.random.choice(['pair_4', 'pair_6', 'impair_5'], 100).tolist(),
            'INDEX3': np.random.choice(['PLAYER', 'BANKER', 'TIE'], 100).tolist(),
            'INDEX5': [],
            'INDEX2_INDEX3': [],
            'INDEX1_INDEX3': [],
            'INDEX10': []
        }

        # Générer INDEX5 basé sur les autres indices
        for i in range(100):
            index5_val = f"{test_sequences['INDEX1'][i]}_{test_sequences['INDEX2'][i]}_{test_sequences['INDEX3'][i]}"
            test_sequences['INDEX5'].append(index5_val)

            # Générer INDEX2_INDEX3
            index2_index3_val = f"{test_sequences['INDEX2'][i]}_{test_sequences['INDEX3'][i]}"
            test_sequences['INDEX2_INDEX3'].append(index2_index3_val)

            # Générer INDEX1_INDEX3
            index1_index3_val = f"{test_sequences['INDEX1'][i]}_{test_sequences['INDEX3'][i]}"
            test_sequences['INDEX1_INDEX3'].append(index1_index3_val)

            # Générer INDEX10 (simple INDEX2_INDEX3)
            test_sequences['INDEX10'].append(index2_index3_val)
        
        # 3. TESTER INDEX5_ANALYZER
        print("\n   🔄 Test INDEX5_ANALYZER...")
        try:
            from lupasco_refactored.analyzers.index5_analyzer import Index5Analyzer

            index5_analyzer = Index5Analyzer(test_sequences)
            resultats_index5 = index5_analyzer.analyser_index5_avec_formules_exactes()

            print(f"   ✅ INDEX5_ANALYZER réussi")

        except Exception as e:
            print(f"   ❌ ERREUR INDEX5_ANALYZER : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR TROUVÉE DANS INDEX5_ANALYZER !")
                traceback.print_exc()
                return False

        # 4. TESTER INDEX2_INDEX3_ANALYZER
        print("\n   🔄 Test INDEX2_INDEX3_ANALYZER...")
        try:
            from lupasco_refactored.analyzers.index2_index3_analyzer import Index2Index3Analyzer

            index2_index3_analyzer = Index2Index3Analyzer(test_sequences)
            resultats_index2_index3 = index2_index3_analyzer.analyser_index2_index3_avec_formules_exactes()

            print(f"   ✅ INDEX2_INDEX3_ANALYZER réussi")

        except Exception as e:
            print(f"   ❌ ERREUR INDEX2_INDEX3_ANALYZER : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR TROUVÉE DANS INDEX2_INDEX3_ANALYZER !")
                traceback.print_exc()
                return False

        # 5. TESTER INDEX1_INDEX3_ANALYZER
        print("\n   🔄 Test INDEX1_INDEX3_ANALYZER...")
        try:
            from lupasco_refactored.analyzers.index1_index3_analyzer import Index1Index3Analyzer

            index1_index3_analyzer = Index1Index3Analyzer(test_sequences)
            resultats_index1_index3 = index1_index3_analyzer.analyser_index1_index3_avec_formules_exactes()

            print(f"   ✅ INDEX1_INDEX3_ANALYZER réussi")

        except Exception as e:
            print(f"   ❌ ERREUR INDEX1_INDEX3_ANALYZER : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR TROUVÉE DANS INDEX1_INDEX3_ANALYZER !")
                traceback.print_exc()
                return False
        
        # 6. TESTER AVEC LUPASCO_ENTROPY_ANALYSIS DIRECTEMENT
        print("\n   🔄 Test LUPASCO_ENTROPY_ANALYSIS...")
        try:
            from formules_mathematiques_exactes import lupasco_entropy_analysis

            resultats_lupasco = lupasco_entropy_analysis(
                test_sequences['INDEX1'],
                test_sequences['INDEX2'],
                test_sequences['INDEX3']
            )

            print(f"   ✅ LUPASCO_ENTROPY_ANALYSIS réussi")

        except Exception as e:
            print(f"   ❌ ERREUR LUPASCO_ENTROPY_ANALYSIS : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR TROUVÉE DANS LUPASCO_ENTROPY_ANALYSIS !")
                traceback.print_exc()
                return False
        
        # 8. TESTER AVEC ENTROPY_ADVANCED
        print("\n   🔄 Test ENTROPY_ADVANCED...")
        try:
            from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy
            
            entropy_analyzer = AdvancedEntropy()
            
            # Tester différentes méthodes
            resultats_entropy = entropy_analyzer.analyser_entropie_jointe_complete(test_sequences)
            
            print(f"   ✅ ENTROPY_ADVANCED réussi")
            
        except Exception as e:
            print(f"   ❌ ERREUR ENTROPY_ADVANCED : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR TROUVÉE DANS ENTROPY_ADVANCED !")
                traceback.print_exc()
                return False
        
        print("\n🏆 TOUS LES TESTS RÉUSSIS - AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR GÉNÉRALE : {e}")
        if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
            print("🎯 ERREUR 'int' + 'dict' TROUVÉE !")
            traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DE REPRODUCTION COMPLÈTE")
    print("=" * 50)
    
    succes = test_reproduction_erreur_complete()
    
    print("\n" + "=" * 50)
    if succes:
        print("✅ AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE")
        print("Toutes les corrections semblent fonctionner.")
        sys.exit(0)
    else:
        print("❌ ERREUR 'int' + 'dict' REPRODUITE")
        print("L'erreur a été localisée et doit être corrigée.")
        sys.exit(1)
