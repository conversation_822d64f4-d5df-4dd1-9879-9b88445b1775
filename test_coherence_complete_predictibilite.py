#!/usr/bin/env python3
"""
TEST DE COHÉRENCE COMPLÈTE - ANALYSE PRÉDICTIBILITÉ INDEX3 PAR INDEX1
=====================================================================

Ce script vérifie la cohérence complète de toutes les sections du programme
liées à l'analyse de prédictibilité INDEX3 par INDEX1 et l'utilisation des formules exactes.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np
import tempfile
import io
from contextlib import redirect_stdout

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_coherence_complete():
    """
    Test de cohérence complète de toutes les sections liées à l'analyse de prédictibilité
    """
    print("🔬 TEST DE COHÉRENCE COMPLÈTE - ANALYSE PRÉDICTIBILITÉ INDEX3 PAR INDEX1")
    print("=" * 80)
    
    resultats_tests = {}
    
    try:
        # 1. VÉRIFICATION DES IMPORTS ET FORMULES
        print("\n📊 PHASE 1: VÉRIFICATION DES IMPORTS ET FORMULES")
        print("-" * 55)
        
        # Test des imports dans lupasco_analyzer.py
        try:
            from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
            from lupasco_refactored.utils.math_formulas import information_mutuelle_corrigee, entropie_conditionnelle_corrigee
            from formules_mathematiques_exactes import information_mutuelle_exacte, entropie_conditionnelle_exacte
            print("✅ Tous les imports requis sont disponibles")
            resultats_tests['imports'] = True
        except ImportError as e:
            print(f"❌ Erreur d'import : {e}")
            resultats_tests['imports'] = False
            return False
        
        # 2. VÉRIFICATION DU FLUX D'EXÉCUTION PRINCIPAL
        print("\n🎯 PHASE 2: VÉRIFICATION DU FLUX D'EXÉCUTION")
        print("-" * 50)
        
        # Créer des données de test
        np.random.seed(42)
        sequences_test = {
            'INDEX1': np.random.choice(['SYNC', 'DESYNC'], 1000).tolist(),
            'INDEX3': np.random.choice(['PLAYER', 'BANKER', 'TIE'], 1000).tolist()
        }
        
        # Test via analyseur.py (wrapper)
        try:
            from analyseur import AnalyseurSequencesLupasco
            
            # Créer un analyseur temporaire
            analyseur_legacy = AnalyseurSequencesLupasco.__new__(AnalyseurSequencesLupasco)
            analyseur_legacy.sequences = sequences_test
            analyseur_legacy.resultats = {}
            
            # Capturer la sortie
            captured_output = io.StringIO()
            with redirect_stdout(captured_output):
                resultats_legacy = analyseur_legacy.analyser_predictibilite_index3_par_index1()
            
            output_legacy = captured_output.getvalue()
            
            # Vérifier le message des formules exactes
            message_formules_legacy = "✅ Utilisation des formules exactes pour l'information mutuelle" in output_legacy
            titre_analyse_legacy = "🔬 ANALYSE PRÉDICTIBILITÉ INDEX3 PAR INDEX1" in output_legacy
            
            print(f"   Wrapper analyseur.py - Message formules : {'✅' if message_formules_legacy else '❌'}")
            print(f"   Wrapper analyseur.py - Titre analyse : {'✅' if titre_analyse_legacy else '❌'}")
            
            resultats_tests['wrapper_legacy'] = message_formules_legacy and titre_analyse_legacy
            
        except Exception as e:
            print(f"   ❌ Erreur wrapper legacy : {e}")
            resultats_tests['wrapper_legacy'] = False
        
        # Test via base_analyzer.py
        try:
            from lupasco_refactored.core.base_analyzer import AnalyseurSequencesLupasco as BaseAnalyzer
            
            # Créer un analyseur de base temporaire
            analyseur_base = BaseAnalyzer.__new__(BaseAnalyzer)
            analyseur_base.sequences = sequences_test
            analyseur_base.resultats = {}
            
            # Initialiser l'analyseur Lupasco
            from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
            analyseur_base.lupasco_analyzer = LupascoAnalyzer(sequences_test)
            
            # Capturer la sortie
            captured_output = io.StringIO()
            with redirect_stdout(captured_output):
                resultats_base = analyseur_base.analyser_predictibilite_index3_par_index1()
            
            output_base = captured_output.getvalue()
            
            # Vérifier le message des formules exactes
            message_formules_base = "✅ Utilisation des formules exactes pour l'information mutuelle" in output_base
            titre_analyse_base = "🔬 ANALYSE PRÉDICTIBILITÉ INDEX3 PAR INDEX1" in output_base
            
            print(f"   Base analyzer - Message formules : {'✅' if message_formules_base else '❌'}")
            print(f"   Base analyzer - Titre analyse : {'✅' if titre_analyse_base else '❌'}")
            
            resultats_tests['base_analyzer'] = message_formules_base and titre_analyse_base
            
        except Exception as e:
            print(f"   ❌ Erreur base analyzer : {e}")
            resultats_tests['base_analyzer'] = False
        
        # 3. VÉRIFICATION DE LA COHÉRENCE DES FORMULES
        print("\n🔬 PHASE 3: VÉRIFICATION DE LA COHÉRENCE DES FORMULES")
        print("-" * 60)
        
        # Comparer les formules exactes et corrigées
        index1_test = sequences_test['INDEX1']
        index3_test = sequences_test['INDEX3']
        
        mi_exacte = information_mutuelle_exacte(index1_test, index3_test)
        mi_corrigee = information_mutuelle_corrigee(index1_test, index3_test)
        
        ec_exacte = entropie_conditionnelle_exacte(index3_test, index1_test)
        ec_corrigee = entropie_conditionnelle_corrigee(index3_test, index1_test)
        
        diff_mi = abs(mi_exacte - mi_corrigee)
        diff_ec = abs(ec_exacte - ec_corrigee)
        
        coherence_formules = diff_mi < 1e-10 and diff_ec < 1e-10
        
        print(f"   Information mutuelle - Différence : {diff_mi:.2e}")
        print(f"   Entropie conditionnelle - Différence : {diff_ec:.2e}")
        print(f"   Cohérence formules : {'✅' if coherence_formules else '❌'}")
        
        resultats_tests['coherence_formules'] = coherence_formules
        
        # 4. VÉRIFICATION DU RAPPORT
        print("\n📝 PHASE 4: VÉRIFICATION DU RAPPORT")
        print("-" * 40)
        
        try:
            from lupasco_refactored.reporting.report_generator import ReportGenerator
            
            # Utiliser les résultats du test précédent
            resultats_pour_rapport = {
                'PREDICTIBILITE_INDEX3_PAR_INDEX1': resultats_legacy if 'resultats_legacy' in locals() else resultats_base
            }
            
            # Créer un fichier temporaire
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
                tmp_filename = tmp_file.name
            
            try:
                report_gen = ReportGenerator(
                    resultats=resultats_pour_rapport,
                    sequences=sequences_test,
                    fichier_json="test.json",
                    nb_parties_total=100
                )
                
                rapport_file = report_gen.generer_rapport(tmp_filename)
                
                # Lire et analyser le rapport
                with open(rapport_file, 'r', encoding='utf-8') as f:
                    contenu_rapport = f.read()
                
                # Vérifications du rapport
                section_presente = "PRÉDICTIBILITÉ INDEX3 PAR INDEX1" in contenu_rapport
                info_mutuelle_presente = "Information mutuelle :" in contenu_rapport
                score_predictibilite_present = "Score de prédictibilité :" in contenu_rapport
                
                print(f"   Section prédictibilité : {'✅' if section_presente else '❌'}")
                print(f"   Information mutuelle : {'✅' if info_mutuelle_presente else '❌'}")
                print(f"   Score prédictibilité : {'✅' if score_predictibilite_present else '❌'}")
                
                resultats_tests['rapport'] = section_presente and info_mutuelle_presente and score_predictibilite_present
                
            finally:
                if os.path.exists(tmp_filename):
                    os.unlink(tmp_filename)
                    
        except Exception as e:
            print(f"   ❌ Erreur génération rapport : {e}")
            resultats_tests['rapport'] = False
        
        # 5. VÉRIFICATION DES INTERCONNEXIONS
        print("\n🔗 PHASE 5: VÉRIFICATION DES INTERCONNEXIONS")
        print("-" * 50)
        
        # Vérifier que les autres modules utilisent aussi les formules exactes
        try:
            from lupasco_refactored.analyzers.index1_index3_analyzer import Index1Index3Analyzer
            
            # Test de l'analyseur INDEX1_INDEX3
            index13_analyzer = Index1Index3Analyzer(sequences_test)
            
            # Vérifier l'import des formules exactes
            import inspect
            source_code = inspect.getsource(Index1Index3Analyzer)
            import_formules_exactes = "from formules_mathematiques_exactes import" in source_code
            
            print(f"   Index1Index3Analyzer - Import formules exactes : {'✅' if import_formules_exactes else '❌'}")
            
            resultats_tests['interconnexions'] = import_formules_exactes
            
        except Exception as e:
            print(f"   ❌ Erreur vérification interconnexions : {e}")
            resultats_tests['interconnexions'] = False
        
        # 6. CALCUL DU SCORE GLOBAL
        print("\n🏆 PHASE 6: ÉVALUATION GLOBALE")
        print("-" * 35)
        
        score_global = sum(resultats_tests.values()) / len(resultats_tests)
        
        print(f"\nRésultats détaillés :")
        for test, resultat in resultats_tests.items():
            print(f"   {test:20} : {'✅' if resultat else '❌'}")
        
        print(f"\nScore global : {score_global:.1%}")
        
        if score_global >= 0.8:
            print("\n🎉 COHÉRENCE COMPLÈTE VALIDÉE")
            print("Toutes les sections liées à l'analyse de prédictibilité INDEX3 par INDEX1")
            print("utilisent correctement les formules exactes et sont cohérentes.")
            return True
        else:
            print("\n⚠️ PROBLÈMES DE COHÉRENCE DÉTECTÉS")
            print("Certaines sections ne sont pas cohérentes ou n'utilisent pas les formules exactes.")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DE COHÉRENCE COMPLÈTE")
    print("=" * 45)
    
    succes = test_coherence_complete()
    
    print("\n" + "=" * 45)
    if succes:
        print("✅ TEST DE COHÉRENCE RÉUSSI")
        print("Le programme est entièrement cohérent concernant l'analyse de prédictibilité.")
        sys.exit(0)
    else:
        print("❌ TEST DE COHÉRENCE ÉCHOUÉ")
        print("Des incohérences ont été détectées dans le programme.")
        sys.exit(1)
