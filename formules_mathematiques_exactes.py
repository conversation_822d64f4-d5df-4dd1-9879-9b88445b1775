# -*- coding: utf-8 -*-
"""
FORMULES MATHÉMATIQUES EXACTES POUR L'ANALYSE STATISTIQUE
=========================================================

Ce fichier contient les formules mathématiques exactes vérifiées à partir de sources 
académiques fiables (Wikipedia, sources universitaires) pour l'analyse statistique 
des données de baccarat et du système Lupasco.

Toutes les formules sont implémentées avec les bibliothèques Python validées :
- numpy : calculs numériques
- scipy.stats : tests statistiques
- pandas : manipulation de données
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import entropy
import warnings

# ============================================================================
# 1. COEFFICIENT DE GINI
# ============================================================================

def gini_coefficient(x):
    """
    Calcule le coefficient de Gini exact.
    
    Formule exacte : G = (2 * Σ(i * x_i)) / (n * Σ(x_i)) - (n + 1) / n
    où x_i sont les valeurs triées par ordre croissant.
    
    Source : Wikipedia - Gini coefficient
    
    Args:
        x (array-like): Données à analyser
        
    Returns:
        float: Coefficient de Gini (0 = égalité parfaite, 1 = inégalité maximale)
    """
    x = np.array(x, dtype=float)
    x = x[~np.isnan(x)]  # Supprimer les NaN
    
    if len(x) == 0:
        return np.nan
    
    # Trier les valeurs par ordre croissant
    x_sorted = np.sort(x)
    n = len(x_sorted)
    
    # Formule exacte du coefficient de Gini
    numerator = 2 * np.sum((np.arange(1, n + 1) * x_sorted))
    denominator = n * np.sum(x_sorted)
    
    if denominator == 0:
        return 0.0
    
    gini = (numerator / denominator) - (n + 1) / n
    return gini


# ============================================================================
# 2. ENTROPIE DE SHANNON
# ============================================================================

def shannon_entropy(probabilities):
    """
    Calcule l'entropie de Shannon exacte.
    
    Formule exacte : H(X) = -Σ(p_i * log₂(p_i))
    où p_i sont les probabilités des événements.
    
    Source : Shannon, C.E. (1948). "A Mathematical Theory of Communication"
    
    Args:
        probabilities (array-like): Probabilités des événements
        
    Returns:
        float: Entropie de Shannon en bits
    """
    p = np.array(probabilities, dtype=float)
    p = p[p > 0]  # Supprimer les probabilités nulles
    
    if len(p) == 0:
        return 0.0
    
    # Formule exacte de l'entropie de Shannon
    return -np.sum(p * np.log2(p))


def shannon_entropy_from_data(data):
    """
    Calcule l'entropie de Shannon à partir de données brutes.
    
    Args:
        data (array-like): Données brutes
        
    Returns:
        float: Entropie de Shannon en bits
    """
    unique, counts = np.unique(data, return_counts=True)
    probabilities = counts / len(data)
    return shannon_entropy(probabilities)


# ============================================================================
# 3. FONCTION D'AUTOCORRÉLATION
# ============================================================================

def autocorrelation_function(x, max_lag=None):
    """
    Calcule la fonction d'autocorrélation exacte.
    
    Formule exacte pour processus stationnaire au sens large :
    ρ_XX(τ) = E[(X_{t+τ} - μ)(X_t - μ)] / σ²
    
    Source : Wikipedia - Autocorrelation
    
    Args:
        x (array-like): Série temporelle
        max_lag (int): Nombre maximum de décalages à calculer
        
    Returns:
        numpy.ndarray: Fonction d'autocorrélation
    """
    x = np.array(x, dtype=float)
    n = len(x)
    
    if max_lag is None:
        max_lag = min(n // 4, 40)  # Limite raisonnable
    
    # Centrer la série
    x_centered = x - np.mean(x)
    variance = np.var(x, ddof=1)
    
    if variance == 0:
        return np.ones(max_lag + 1)
    
    autocorr = np.zeros(max_lag + 1)
    
    for lag in range(max_lag + 1):
        if lag == 0:
            autocorr[lag] = 1.0
        else:
            if n - lag > 0:
                covariance = np.mean(x_centered[:-lag] * x_centered[lag:])
                autocorr[lag] = covariance / variance
            else:
                autocorr[lag] = 0.0
    
    return autocorr


# ============================================================================
# 4. COEFFICIENT DE VARIATION
# ============================================================================

def coefficient_of_variation(x):
    """
    Calcule le coefficient de variation exact.
    
    Formule exacte : CV = σ / μ
    où σ est l'écart-type et μ la moyenne.
    
    Source : Wikipedia - Coefficient of variation
    
    Args:
        x (array-like): Données à analyser
        
    Returns:
        float: Coefficient de variation
    """
    x = np.array(x, dtype=float)
    x = x[~np.isnan(x)]
    
    if len(x) == 0:
        return np.nan
    
    mean_val = np.mean(x)
    
    if mean_val == 0:
        return np.inf if np.std(x, ddof=1) > 0 else np.nan
    
    std_val = np.std(x, ddof=1)
    return std_val / abs(mean_val)


# ============================================================================
# 5. DÉTECTION D'ANOMALIES (Z-SCORE)
# ============================================================================

def z_score(data):
    """
    Calcule le Z-score pour détecter les anomalies.

    Formule exacte : Z = (x - μ) / σ
    où μ est la moyenne et σ l'écart-type.

    Args:
        data (array-like): Données à analyser

    Returns:
        np.ndarray: Z-scores pour chaque valeur
    """
    data = np.array(data)
    if len(data) == 0:
        return np.array([])

    # 🔧 CORRECTION : Gérer le cas d'un seul élément
    if len(data) == 1:
        return np.zeros_like(data)

    mean = np.mean(data)
    std = np.std(data, ddof=1)  # Écart-type corrigé

    # 🔧 CORRECTION : Gérer std == 0 ET std == nan
    if std == 0 or np.isnan(std):
        return np.zeros_like(data)

    # Calculer les z-scores
    z_scores = (data - mean) / std

    # 🔧 NOUVELLE CORRECTION : Limiter les z-scores aberrants
    # Valeurs > 10 ou < -10 sont statistiquement impossibles dans la plupart des cas
    z_scores = np.clip(z_scores, -10.0, 10.0)

    return z_scores


def detect_anomalies(data, threshold=2.0):
    """
    Détecte les anomalies basées sur le Z-score.

    Args:
        data (array-like): Données à analyser
        threshold (float): Seuil pour détecter les anomalies (défaut: 2.0)

    Returns:
        dict: {
            'z_scores': Z-scores,
            'anomalies_indices': Indices des anomalies,
            'anomalies_values': Valeurs anormales,
            'normal_indices': Indices des valeurs normales
        }
    """
    z_scores = z_score(data)
    anomalies_mask = np.abs(z_scores) > threshold

    return {
        'z_scores': z_scores,
        'anomalies_indices': np.where(anomalies_mask)[0],
        'anomalies_values': np.array(data)[anomalies_mask],
        'normal_indices': np.where(~anomalies_mask)[0],
        'threshold': threshold
    }


def z_score_multinomial(observed_frequencies, expected_frequencies=None):
    """
    Calcule le Z-score standardisé pour un test multinomial (distribution avec k>2 catégories).

    🔧 CORRECTION : Utilise la formule de standardisation classique pour les résidus de Pearson
    Z_i = (O_i - E_i) / sqrt(E_i)
    où O_i = fréquence observée, E_i = fréquence attendue

    Cette formule est appropriée pour comparer les déviations relatives à l'attendu.

    Args:
        observed_frequencies (array-like): Fréquences observées
        expected_frequencies (array-like, optional): Fréquences attendues (uniforme si None)

    Returns:
        np.ndarray: Z-scores standardisés pour chaque catégorie
    """
    observed = np.array(observed_frequencies, dtype=float)
    n_total = np.sum(observed)
    k_categories = len(observed)

    if expected_frequencies is None:
        # Distribution uniforme attendue
        expected = np.full(k_categories, n_total / k_categories)
    else:
        expected = np.array(expected_frequencies, dtype=float)
        # Normaliser pour que la somme soit égale au total observé
        expected = expected * (n_total / np.sum(expected))

    # Éviter les divisions par zéro
    mask_valid = expected > 0
    z_scores = np.zeros_like(observed)

    # Calcul du z-score standardisé (résidus de Pearson standardisés)
    for i in range(k_categories):
        if mask_valid[i]:
            # Formule simplifiée et robuste
            z_scores[i] = (observed[i] - expected[i]) / np.sqrt(expected[i])
        else:
            z_scores[i] = 0.0

    # 🔧 CORRECTION : Pas de limitation artificielle pour les vrais z-scores
    # Les grandes valeurs sont mathématiquement correctes pour des données très déséquilibrées
    # Limitation uniquement pour éviter les overflows numériques
    z_scores = np.clip(z_scores, -1000.0, 1000.0)

    return z_scores


# ============================================================================
# 6. TEST DES RUNS (WALD-WOLFOWITZ)
# ============================================================================

def runs_test(sequence):
    """
    Effectue le test des runs de Wald-Wolfowitz pour la randomness.

    Formule exacte pour la statistique Z :
    Z = (R - E[R]) / √Var[R]
    où R est le nombre de runs observés.

    Source : Wald, A. and Wolfowitz, J. (1940)

    Args:
        sequence (array-like): Séquence binaire ou convertible en binaire

    Returns:
        dict: Résultats du test (statistique, p-value, etc.)
    """
    sequence = np.array(sequence)

    # Convertir en séquence binaire si nécessaire
    unique_vals = np.unique(sequence)

    if len(unique_vals) == 2:
        # Déjà binaire, mapper vers 0 et 1
        binary_seq = (sequence == unique_vals[1]).astype(int)
    else:
        # Plus de 2 valeurs, convertir en binaire basé sur la médiane
        try:
            # Pour les données numériques
            if sequence.dtype.kind in 'biufc':  # numeric types
                median_val = np.median(sequence)
                binary_seq = (sequence > median_val).astype(int)
            else:
                # Pour les données string, utiliser l'ordre alphabétique
                sorted_vals = sorted(unique_vals)
                median_idx = len(sorted_vals) // 2
                median_val = sorted_vals[median_idx]
                binary_seq = (sequence >= median_val).astype(int)
        except:
            # Fallback: utiliser la première valeur comme référence
            binary_seq = (sequence == unique_vals[0]).astype(int)
    
    # Compter les runs
    runs = 1
    for i in range(1, len(binary_seq)):
        if binary_seq[i] != binary_seq[i-1]:
            runs += 1
    
    # Compter les éléments de chaque type
    n1 = np.sum(binary_seq == 0)
    n2 = np.sum(binary_seq == 1)
    n = n1 + n2
    
    if n1 == 0 or n2 == 0:
        return {
            'statistic': np.nan,
            'pvalue': np.nan,
            'runs_observed': runs,
            'runs_expected': np.nan
        }
    
    # Formules exactes pour l'espérance et la variance avec gestion des overflows
    try:
        # Utiliser des calculs en float64 pour éviter les overflows
        n1_f = float(n1)
        n2_f = float(n2)
        n_f = float(n)

        expected_runs = (2.0 * n1_f * n2_f) / n_f + 1.0

        # Calcul de la variance avec protection contre overflow
        numerator = 2.0 * n1_f * n2_f * (2.0 * n1_f * n2_f - n_f)
        denominator = n_f * n_f * (n_f - 1.0)

        if denominator == 0 or numerator < 0:
            variance_runs = 0.0
        else:
            variance_runs = numerator / denominator

        # Vérification de validité
        if variance_runs <= 0 or np.isnan(variance_runs) or np.isinf(variance_runs):
            return {
                'statistic': np.nan,
                'pvalue': np.nan,
                'runs_observed': runs,
                'runs_expected': expected_runs,
                'n1': n1,
                'n2': n2,
                'error': 'Variance invalide ou overflow'
            }

        # Statistique Z avec correction de continuité
        std_runs = np.sqrt(variance_runs)
        if std_runs == 0:
            z_stat = 0.0
        else:
            if runs > expected_runs:
                z_stat = (runs - 0.5 - expected_runs) / std_runs
            else:
                z_stat = (runs + 0.5 - expected_runs) / std_runs

        # Limiter z_stat pour éviter les problèmes numériques
        z_stat = np.clip(z_stat, -10, 10)

        # P-value bilatérale (formule statistique exacte avec correction numérique)
        if np.isnan(z_stat) or np.isinf(z_stat):
            p_value = np.nan
        else:
            # 🔧 CORRECTION : Utiliser la fonction de survie pour éviter p-value = 0.0
            # Pour grandes valeurs de |z|, stats.norm.cdf(|z|) → 1.0 exactement
            # Utiliser stats.norm.sf(|z|) = 1 - stats.norm.cdf(|z|) avec meilleure précision
            abs_z = abs(z_stat)
            if abs_z > 8.0:  # Pour z > 8, utiliser la fonction de survie
                p_value = 2.0 * stats.norm.sf(abs_z)
            else:
                p_value = 2.0 * (1.0 - stats.norm.cdf(abs_z))

            # Assurer une p-value minimale pour éviter 0.0 exact
            p_value = max(p_value, 1e-16)  # Limite de précision machine

    except (OverflowError, ZeroDivisionError, ValueError) as e:
        return {
            'statistic': np.nan,
            'pvalue': np.nan,
            'runs_observed': runs,
            'runs_expected': np.nan,
            'n1': n1,
            'n2': n2,
            'error': f'Erreur de calcul: {str(e)}'
        }
    
    return {
        'statistic': z_stat,
        'pvalue': p_value,
        'runs_observed': runs,
        'runs_expected': expected_runs,
        'n1': n1,
        'n2': n2,
        'interpretation': interpret_pvalue(p_value)
    }


# ============================================================================
# 6.1. INTERPRÉTATION DES P-VALUES
# ============================================================================

def interpret_pvalue(p_value):
    """
    Interprète une p-value selon les standards statistiques.

    Args:
        p_value (float): P-value à interpréter

    Returns:
        dict: Interprétation de la p-value
    """
    if np.isnan(p_value):
        return {
            'significance': 'Indéterminé',
            'symbol': 'N/A',
            'interpretation': 'P-value non calculable'
        }

    if p_value > 0.05:
        return {
            'significance': 'Non significatif',
            'symbol': 'ns',
            'interpretation': 'Pas de preuve contre l\'hypothèse nulle (séquence aléatoire)'
        }
    elif p_value > 0.01:
        return {
            'significance': 'Significatif',
            'symbol': '*',
            'interpretation': 'Preuve modérée contre l\'aléatoire (p ≤ 0.05)'
        }
    elif p_value > 0.001:
        return {
            'significance': 'Très significatif',
            'symbol': '**',
            'interpretation': 'Preuve forte contre l\'aléatoire (p ≤ 0.01)'
        }
    elif p_value > 0.0001:
        return {
            'significance': 'Hautement significatif',
            'symbol': '***',
            'interpretation': 'Preuve très forte contre l\'aléatoire (p ≤ 0.001)'
        }
    else:
        return {
            'significance': 'Extrêmement significatif',
            'symbol': '****',
            'interpretation': f'Preuve extrême contre l\'aléatoire (p ≤ 0.0001, p = {p_value:.2e})'
        }

def format_pvalue_for_report(p_value):
    """
    Formate une p-value pour les rapports selon les standards scientifiques.

    Args:
        p_value (float): P-value à formater

    Returns:
        str: P-value formatée
    """
    if np.isnan(p_value):
        return "N/A"
    elif p_value <= 1e-16:
        return "< 1e-16"  # Limite de précision machine
    elif p_value < 1e-10:
        return f"{p_value:.2e}"  # Notation scientifique pour très petites valeurs
    elif p_value < 0.001:
        return f"{p_value:.2e}"  # Notation scientifique pour petites valeurs
    elif p_value < 0.01:
        return f"{p_value:.4f}"  # 4 décimales pour valeurs moyennes
    else:
        return f"{p_value:.6f}"  # 6 décimales pour valeurs normales


def evaluer_aleatoire_expert(stats_globales, stats_detaillees, nb_combinaisons_attendues):
    """
    Évaluation experte multi-critères de l'aléatoire selon les standards statistiques académiques.

    Cette fonction corrige la logique défaillante mono-critère en implémentant une approche
    pondérée qui prend en compte tous les indicateurs statistiques pertinents.

    Args:
        stats_globales (dict): Statistiques globales (entropie, gini, autocorr, runs_test)
        stats_detaillees (list): Liste des analyses détaillées par combinaison
        nb_combinaisons_attendues (int): Nombre de combinaisons théoriques

    Returns:
        dict: {
            'est_aleatoire': bool,
            'score_composite': float,
            'criteres_individuels': dict,
            'justification': str,
            'details_evaluation': dict
        }
    """

    # Extraction des z-scores maximaux des analyses détaillées
    z_scores_max = []
    if stats_detaillees:
        for analyse in stats_detaillees:
            if isinstance(analyse, dict) and 'z_score_max' in analyse:
                z_scores_max.append(abs(analyse['z_score_max']))

    z_score_global_max = max(z_scores_max) if z_scores_max else 0

    # Critères individuels avec seuils experts basés sur la littérature statistique
    criteres = {
        'runs_test_ok': stats_globales.get('runs_test', {}).get('pvalue', 0) > 0.05,
        'z_scores_normaux': z_score_global_max < 3.0,  # Seuil 3-sigma standard
        'autocorr_faible': abs(stats_globales.get('autocorrelation', {}).get(1, 0)) < 0.1,
        'distribution_equilibree': stats_globales.get('coefficient_gini', 1.0) < 0.3,
        'entropie_elevee': stats_globales.get('entropie_shannon', 0) > (np.log2(nb_combinaisons_attendues) * 0.9)
    }

    # Pondération experte basée sur la significativité statistique
    # Les z-scores ont le poids le plus élevé car ils mesurent les déviations de fréquence
    poids = {
        'z_scores_normaux': 0.4,      # Poids le plus élevé - déviations de fréquence
        'runs_test_ok': 0.25,         # Transitions entre états
        'autocorr_faible': 0.15,      # Mémoire temporelle
        'distribution_equilibree': 0.1, # Équité globale
        'entropie_elevee': 0.1        # Complexité informationnelle
    }

    # Score composite pondéré
    score_aleatoire = sum(poids[critere] * (1 if valeur else 0)
                         for critere, valeur in criteres.items())

    # Seuil expert : 70% des critères pondérés doivent être satisfaits
    est_aleatoire = score_aleatoire >= 0.7

    # Génération de la justification experte
    justification = generer_justification_experte(criteres, score_aleatoire, z_score_global_max)

    return {
        'est_aleatoire': est_aleatoire,
        'score_composite': score_aleatoire,
        'criteres_individuels': criteres,
        'justification': justification,
        'details_evaluation': {
            'z_score_global_max': z_score_global_max,
            'poids_utilises': poids,
            'seuil_acceptation': 0.7
        }
    }


def generer_justification_experte(criteres, score_composite, z_score_max):
    """
    Génère une justification statistique rigoureuse pour la décision d'aléatoire.

    Args:
        criteres (dict): Critères individuels évalués
        score_composite (float): Score composite pondéré
        z_score_max (float): Z-score maximum observé

    Returns:
        str: Justification experte détaillée
    """

    # Identification des violations majeures (critères critiques)
    violations_majeures = []
    violations_mineures = []

    if not criteres['z_scores_normaux']:
        violations_majeures.append(f"Z-scores aberrants (max: {z_score_max:.1f} > 3σ)")

    if not criteres['runs_test_ok']:
        violations_mineures.append("Test des runs significatif (patterns détectés)")

    if not criteres['autocorr_faible']:
        violations_mineures.append("Autocorrélation élevée (mémoire temporelle)")

    if not criteres['distribution_equilibree']:
        violations_mineures.append("Distribution déséquilibrée (Gini élevé)")

    if not criteres['entropie_elevee']:
        violations_mineures.append("Entropie insuffisante (complexité faible)")

    # Logique de justification experte
    if violations_majeures:
        # Les violations majeures (z-scores aberrants) sont rédhibitoires
        return f"NON ALÉATOIRE - {'; '.join(violations_majeures)}"
    elif score_composite < 0.7:
        # Score composite insuffisant
        violations_str = '; '.join(violations_mineures) if violations_mineures else "Critères multiples"
        return f"NON ALÉATOIRE - Score: {score_composite:.2f}/1.0 ({violations_str})"
    else:
        # Tous critères satisfaits
        return f"ALÉATOIRE - Score: {score_composite:.2f}/1.0 (tous critères satisfaits)"


def afficher_evaluation_experte(evaluation):
    """
    Affiche l'évaluation experte de manière structurée pour les rapports.

    Args:
        evaluation (dict): Résultat de evaluer_aleatoire_expert()

    Returns:
        str: Affichage formaté pour inclusion dans les rapports
    """

    criteres = evaluation['criteres_individuels']
    details = evaluation['details_evaluation']

    # Symboles pour l'affichage
    def symbole(condition):
        return "✅" if condition else "❌"

    affichage = []
    affichage.append("📊 ÉVALUATION MULTI-CRITÈRES DE L'ALÉATOIRE :")
    affichage.append(f"   {symbole(criteres['runs_test_ok'])} Test des runs p-value (>0.05)")
    affichage.append(f"   {symbole(criteres['z_scores_normaux'])} Z-scores normaux (<3.0) - Max: {details['z_score_global_max']:.1f}")
    affichage.append(f"   {symbole(criteres['autocorr_faible'])} Autocorrélation faible (<0.1)")
    affichage.append(f"   {symbole(criteres['distribution_equilibree'])} Distribution équilibrée (Gini <0.3)")
    affichage.append(f"   {symbole(criteres['entropie_elevee'])} Entropie élevée (>90% max)")
    affichage.append("")
    affichage.append(f"Score composite aléatoire : {evaluation['score_composite']:.2f}/1.0")
    affichage.append(f"Séquence aléatoire : {'OUI' if evaluation['est_aleatoire'] else 'NON'}")
    affichage.append(f"Justification : {evaluation['justification']}")

    return "\n".join(affichage)


# ============================================================================
# 7. FONCTIONS UTILITAIRES POUR L'ANALYSE LUPASCO
# ============================================================================

def lupasco_entropy_analysis(index1_data, index2_data, index3_data):
    """
    Analyse complète de l'entropie pour le système Lupasco.
    
    Args:
        index1_data: Données INDEX1 (SYNC/DESYNC)
        index2_data: Données INDEX2 (pair_4/pair_6/impair_5)
        index3_data: Données INDEX3 (PLAYER/BANKER/TIE)
        
    Returns:
        dict: Résultats de l'analyse entropique
    """
    results = {}
    
    # Entropie de chaque index
    results['entropy_index1'] = shannon_entropy_from_data(index1_data)
    results['entropy_index2'] = shannon_entropy_from_data(index2_data)
    results['entropy_index3'] = shannon_entropy_from_data(index3_data)
    
    # Entropies jointes pour toutes les paires
    joint_12 = list(zip(index1_data, index2_data))
    joint_13 = list(zip(index1_data, index3_data))
    joint_23 = list(zip(index2_data, index3_data))

    results['entropy_joint_12'] = shannon_entropy_from_data(joint_12)
    results['entropy_joint_13'] = shannon_entropy_from_data(joint_13)
    results['entropy_joint_23'] = shannon_entropy_from_data(joint_23)

    # Informations mutuelles pour toutes les paires
    results['mutual_info_12'] = (results['entropy_index1'] +
                                results['entropy_index2'] -
                                results['entropy_joint_12'])

    results['mutual_info_13'] = (results['entropy_index1'] +
                                results['entropy_index3'] -
                                results['entropy_joint_13'])

    results['mutual_info_23'] = (results['entropy_index2'] +
                                results['entropy_index3'] -
                                results['entropy_joint_23'])

    return results


def lupasco_statistical_summary(data_dict):
    """
    Résumé statistique complet pour les données Lupasco.
    
    Args:
        data_dict (dict): Dictionnaire contenant les séries de données
        
    Returns:
        pandas.DataFrame: Résumé statistique
    """
    summary_data = []
    
    for name, data in data_dict.items():
        data_array = np.array(data)
        
        summary = {
            'Variable': name,
            'Count': len(data_array),
            'Mean': np.mean(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Std': np.std(data_array, ddof=1) if data_array.dtype.kind in 'biufc' else np.nan,
            'CV': coefficient_of_variation(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Gini': gini_coefficient(data_array) if data_array.dtype.kind in 'biufc' else np.nan,
            'Entropy': shannon_entropy_from_data(data_array),
            'Unique_Values': len(np.unique(data_array))
        }
        
        summary_data.append(summary)
    
    return pd.DataFrame(summary_data)


# ============================================================================
# 8. VALIDATION DES FORMULES
# ============================================================================

def validate_formulas():
    """
    Valide les formules implémentées avec des cas de test connus.
    
    Returns:
        dict: Résultats de validation
    """
    validation_results = {}
    
    # Test 1: Coefficient de Gini pour distribution uniforme
    uniform_data = np.ones(100)
    gini_uniform = gini_coefficient(uniform_data)
    validation_results['gini_uniform'] = abs(gini_uniform) < 1e-10
    
    # Test 2: Entropie de Shannon pour distribution uniforme
    uniform_probs = np.ones(4) / 4
    entropy_uniform = shannon_entropy(uniform_probs)
    expected_entropy = 2.0  # log₂(4) = 2
    validation_results['entropy_uniform'] = abs(entropy_uniform - expected_entropy) < 1e-10
    
    # Test 3: Autocorrélation à lag 0
    test_data = np.random.randn(100)
    autocorr = autocorrelation_function(test_data, max_lag=1)
    validation_results['autocorr_lag0'] = abs(autocorr[0] - 1.0) < 1e-10
    
    # Test 4: Coefficient de variation pour données constantes
    constant_data = np.ones(50) * 5
    cv_constant = coefficient_of_variation(constant_data)
    validation_results['cv_constant'] = abs(cv_constant) < 1e-10
    
    return validation_results


if __name__ == "__main__":
    # Validation des formules
    print("Validation des formules mathématiques exactes...")
    results = validate_formulas()
    
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    print("\nToutes les formules sont validées et prêtes à l'utilisation.")
