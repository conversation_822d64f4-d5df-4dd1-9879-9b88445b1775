#!/usr/bin/env python3
"""
TEST DE COHÉRENCE FINALE - VÉRIFICATION DE TOUTES LES MODIFICATIONS
===================================================================

Ce script vérifie que toutes les modifications effectuées sont cohérentes
et que les formules exactes consolidées sont utilisées partout.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_coherence_finale():
    """
    Test de cohérence finale de toutes les modifications
    """
    print("🔬 TEST DE COHÉRENCE FINALE - VÉRIFICATION DES MODIFICATIONS")
    print("=" * 70)
    
    resultats_tests = {}
    
    try:
        # 1. VÉRIFICATION DES FORMULES CONSOLIDÉES
        print("\n📊 PHASE 1: VÉRIFICATION DES FORMULES CONSOLIDÉES")
        print("-" * 55)
        
        from formules_mathematiques_exactes import (
            entropie_conditionnelle_exacte,
            information_mutuelle_exacte,
            entropie_jointe_exacte,
            lupasco_entropy_analysis
        )
        
        print("✅ Formules exactes consolidées disponibles")
        resultats_tests['formules_consolidees'] = True
        
        # 2. VÉRIFICATION DES MODULES MODIFIÉS
        print("\n🔧 PHASE 2: VÉRIFICATION DES MODULES MODIFIÉS")
        print("-" * 50)
        
        # Test entropy_advanced.py
        from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy
        analyzer = AdvancedEntropy()
        
        # Vérifier que les méthodes utilisent les formules exactes
        test_data_x = ['A', 'B'] * 50
        test_data_y = ['1', '2'] * 50
        
        # Test _entropie_conditionnelle
        try:
            ec_result = analyzer._entropie_conditionnelle(test_data_y, test_data_x)
            print(f"   entropy_advanced._entropie_conditionnelle : ✅ ({ec_result:.6f})")
            resultats_tests['entropy_advanced_ec'] = True
        except Exception as e:
            print(f"   entropy_advanced._entropie_conditionnelle : ❌ {e}")
            resultats_tests['entropy_advanced_ec'] = False
        
        # Test _information_mutuelle
        try:
            mi_result = analyzer._information_mutuelle(test_data_x, test_data_y)
            print(f"   entropy_advanced._information_mutuelle : ✅ ({mi_result:.6f})")
            resultats_tests['entropy_advanced_mi'] = True
        except Exception as e:
            print(f"   entropy_advanced._information_mutuelle : ❌ {e}")
            resultats_tests['entropy_advanced_mi'] = False
        
        # Test _entropie_jointe
        try:
            ej_result = analyzer._entropie_jointe(test_data_x, test_data_y)
            print(f"   entropy_advanced._entropie_jointe : ✅ ({ej_result:.6f})")
            resultats_tests['entropy_advanced_ej'] = True
        except Exception as e:
            print(f"   entropy_advanced._entropie_jointe : ❌ {e}")
            resultats_tests['entropy_advanced_ej'] = False
        
        # Test math_formulas.py
        from lupasco_refactored.utils.math_formulas import (
            entropie_conditionnelle_corrigee,
            information_mutuelle_corrigee
        )
        
        try:
            ec_corrigee = entropie_conditionnelle_corrigee(test_data_y, test_data_x)
            print(f"   math_formulas.entropie_conditionnelle_corrigee : ✅ ({ec_corrigee:.6f})")
            resultats_tests['math_formulas_ec'] = True
        except Exception as e:
            print(f"   math_formulas.entropie_conditionnelle_corrigee : ❌ {e}")
            resultats_tests['math_formulas_ec'] = False
        
        try:
            mi_corrigee = information_mutuelle_corrigee(test_data_x, test_data_y)
            print(f"   math_formulas.information_mutuelle_corrigee : ✅ ({mi_corrigee:.6f})")
            resultats_tests['math_formulas_mi'] = True
        except Exception as e:
            print(f"   math_formulas.information_mutuelle_corrigee : ❌ {e}")
            resultats_tests['math_formulas_mi'] = False
        
        # Test analyseur.py
        from analyseur import AnalyseurSequencesLupasco
        
        # Créer un analyseur temporaire
        analyseur_test = AnalyseurSequencesLupasco.__new__(AnalyseurSequencesLupasco)
        analyseur_test.sequences = {}
        analyseur_test.resultats = {}
        
        try:
            ec_analyseur = analyseur_test._entropie_conditionnelle(test_data_y, test_data_x)
            print(f"   analyseur._entropie_conditionnelle : ✅ ({ec_analyseur:.6f})")
            resultats_tests['analyseur_ec'] = True
        except Exception as e:
            print(f"   analyseur._entropie_conditionnelle : ❌ {e}")
            resultats_tests['analyseur_ec'] = False
        
        try:
            mi_analyseur = analyseur_test._information_mutuelle(test_data_x, test_data_y)
            print(f"   analyseur._information_mutuelle : ✅ ({mi_analyseur:.6f})")
            resultats_tests['analyseur_mi'] = True
        except Exception as e:
            print(f"   analyseur._information_mutuelle : ❌ {e}")
            resultats_tests['analyseur_mi'] = False
        
        try:
            ej_analyseur = analyseur_test._entropie_jointe(test_data_x, test_data_y)
            print(f"   analyseur._entropie_jointe : ✅ ({ej_analyseur:.6f})")
            resultats_tests['analyseur_ej'] = True
        except Exception as e:
            print(f"   analyseur._entropie_jointe : ❌ {e}")
            resultats_tests['analyseur_ej'] = False
        
        # 3. VÉRIFICATION DE LA COHÉRENCE NUMÉRIQUE
        print("\n🔢 PHASE 3: VÉRIFICATION DE LA COHÉRENCE NUMÉRIQUE")
        print("-" * 55)
        
        # Comparer les résultats entre les différentes implémentations
        ec_exacte = entropie_conditionnelle_exacte(test_data_y, test_data_x)
        mi_exacte = information_mutuelle_exacte(test_data_x, test_data_y)
        ej_exacte = entropie_jointe_exacte(test_data_x, test_data_y)
        
        # Vérifier la cohérence
        coherences = {
            'ec_entropy_advanced': abs(ec_result - ec_exacte) < 1e-10 if 'ec_result' in locals() else False,
            'ec_math_formulas': abs(ec_corrigee - ec_exacte) < 1e-10 if 'ec_corrigee' in locals() else False,
            'ec_analyseur': abs(ec_analyseur - ec_exacte) < 1e-10 if 'ec_analyseur' in locals() else False,
            'mi_entropy_advanced': abs(mi_result - mi_exacte) < 1e-10 if 'mi_result' in locals() else False,
            'mi_math_formulas': abs(mi_corrigee - mi_exacte) < 1e-10 if 'mi_corrigee' in locals() else False,
            'mi_analyseur': abs(mi_analyseur - mi_exacte) < 1e-10 if 'mi_analyseur' in locals() else False,
            'ej_entropy_advanced': abs(ej_result - ej_exacte) < 1e-10 if 'ej_result' in locals() else False,
            'ej_analyseur': abs(ej_analyseur - ej_exacte) < 1e-10 if 'ej_analyseur' in locals() else False
        }
        
        for test_name, coherent in coherences.items():
            print(f"   Cohérence {test_name} : {'✅' if coherent else '❌'}")
            resultats_tests[f'coherence_{test_name}'] = coherent
        
        # 4. TEST DE L'ANALYSE COMPLÈTE
        print("\n🎯 PHASE 4: TEST DE L'ANALYSE COMPLÈTE")
        print("-" * 40)
        
        # Test de lupasco_entropy_analysis
        try:
            np.random.seed(42)
            index1_test = np.random.choice(['SYNC', 'DESYNC'], 100).tolist()
            index2_test = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 100).tolist()
            index3_test = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 100).tolist()
            
            resultats_lupasco = lupasco_entropy_analysis(index1_test, index2_test, index3_test)
            
            # Vérifier la structure des résultats
            champs_requis = [
                'entropy_index1', 'entropy_index2', 'entropy_index3',
                'mutual_info_12', 'mutual_info_13', 'mutual_info_23',
                'validation', 'complexity_metrics'
            ]
            
            structure_ok = all(champ in resultats_lupasco for champ in champs_requis)
            validation_ok = all(resultats_lupasco['validation'].values())
            
            print(f"   Structure lupasco_entropy_analysis : {'✅' if structure_ok else '❌'}")
            print(f"   Validation interne : {'✅' if validation_ok else '❌'}")
            
            resultats_tests['lupasco_analysis_structure'] = structure_ok
            resultats_tests['lupasco_analysis_validation'] = validation_ok
            
        except Exception as e:
            print(f"   lupasco_entropy_analysis : ❌ {e}")
            resultats_tests['lupasco_analysis_structure'] = False
            resultats_tests['lupasco_analysis_validation'] = False
        
        # 5. CALCUL DU SCORE FINAL
        print("\n🏆 PHASE 5: ÉVALUATION FINALE")
        print("-" * 35)
        
        score_final = sum(resultats_tests.values()) / len(resultats_tests)
        
        print(f"\nRésultats détaillés :")
        for test, resultat in resultats_tests.items():
            print(f"   {test:35} : {'✅' if resultat else '❌'}")
        
        print(f"\nScore final : {score_final:.1%}")
        
        if score_final >= 0.9:
            print("\n🎉 COHÉRENCE FINALE EXCELLENTE")
            print("Toutes les modifications sont cohérentes et utilisent les formules exactes.")
            return True
        elif score_final >= 0.7:
            print("\n⚠️ COHÉRENCE FINALE ACCEPTABLE")
            print("La plupart des modifications sont cohérentes, quelques ajustements mineurs.")
            return True
        else:
            print("\n❌ PROBLÈMES DE COHÉRENCE DÉTECTÉS")
            print("Des corrections importantes sont nécessaires.")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DE COHÉRENCE FINALE")
    print("=" * 45)
    
    succes = test_coherence_finale()
    
    print("\n" + "=" * 45)
    if succes:
        print("✅ TEST DE COHÉRENCE FINALE RÉUSSI")
        print("Toutes les modifications sont cohérentes et fonctionnelles.")
        sys.exit(0)
    else:
        print("❌ TEST DE COHÉRENCE FINALE ÉCHOUÉ")
        print("Des problèmes de cohérence ont été détectés.")
        sys.exit(1)
