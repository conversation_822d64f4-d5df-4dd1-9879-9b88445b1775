#!/usr/bin/env python3
"""
TEST DU RAPPORT D'ANALYSE DE PRÉDICTIBILITÉ INDEX3 PAR INDEX1
=============================================================

Ce script génère et examine le contenu du rapport pour l'analyse de prédictibilité
INDEX3 par INDEX1 pour vérifier que les formules exactes sont mentionnées.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np
import tempfile

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def generer_rapport_predictibilite():
    """
    Génère un rapport complet et examine la section de prédictibilité
    """
    print("📝 GÉNÉRATION ET ANALYSE DU RAPPORT DE PRÉDICTIBILITÉ")
    print("=" * 60)
    
    try:
        # 1. <PERSON><PERSON>er des données de test réalistes
        print("\n📊 PHASE 1: CRÉATION DES DONNÉES DE TEST")
        print("-" * 45)
        
        np.random.seed(42)
        
        # Simuler des données de baccarat réalistes
        index1_data = []
        index3_data = []
        
        # Créer des patterns réalistes avec corrélation
        for i in range(1000):
            if i % 3 == 0:
                # Pattern corrélé
                index1_data.append('SYNC')
                index3_data.append('PLAYER' if np.random.random() > 0.3 else 'BANKER')
            elif i % 3 == 1:
                # Pattern anti-corrélé
                index1_data.append('DESYNC')
                index3_data.append('BANKER' if np.random.random() > 0.4 else 'TIE')
            else:
                # Pattern aléatoire
                index1_data.append(np.random.choice(['SYNC', 'DESYNC']))
                index3_data.append(np.random.choice(['PLAYER', 'BANKER', 'TIE']))
        
        sequences = {
            'INDEX1': index1_data,
            'INDEX3': index3_data,
            'INDEX1_INDEX3': [f"{i1}_{i3}" for i1, i3 in zip(index1_data, index3_data)]
        }
        
        print(f"✅ Données créées : {len(index1_data)} éléments")
        print(f"   INDEX1 unique : {len(set(index1_data))} valeurs")
        print(f"   INDEX3 unique : {len(set(index3_data))} valeurs")
        
        # 2. Exécuter l'analyse de prédictibilité
        print("\n🔬 PHASE 2: ANALYSE DE PRÉDICTIBILITÉ")
        print("-" * 45)
        
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
        
        analyzer = LupascoAnalyzer(sequences)
        
        # Capturer la sortie pour vérifier les messages
        import io
        from contextlib import redirect_stdout
        
        captured_output = io.StringIO()
        
        with redirect_stdout(captured_output):
            resultats_pred = analyzer.analyser_predictibilite_index3_par_index1()
        
        output_text = captured_output.getvalue()
        
        print("Sortie de l'analyse :")
        print("-" * 20)
        print(output_text)
        print("-" * 20)
        
        # Vérifier les résultats
        print(f"Information mutuelle : {resultats_pred.get('information_mutuelle', 'N/A'):.6f}")
        print(f"Score prédictibilité : {resultats_pred.get('score_predictibilite', 'N/A'):.6f}")
        print(f"Réduction incertitude : {resultats_pred.get('reduction_incertitude_pct', 'N/A'):.2f}%")
        print(f"Prédictible : {resultats_pred.get('predictible', 'N/A')}")
        
        # 3. Générer le rapport complet
        print("\n📝 PHASE 3: GÉNÉRATION DU RAPPORT")
        print("-" * 40)
        
        from lupasco_refactored.reporting.report_generator import ReportGenerator
        
        resultats_complets = {
            'PREDICTIBILITE_INDEX3_PAR_INDEX1': resultats_pred
        }
        
        # Créer un fichier temporaire
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_filename = tmp_file.name
        
        try:
            report_gen = ReportGenerator(
                resultats=resultats_complets,
                sequences=sequences,
                fichier_json="test_dataset.json",
                nb_parties_total=100
            )
            
            rapport_file = report_gen.generer_rapport(tmp_filename)
            print(f"✅ Rapport généré : {rapport_file}")
            
            # 4. Analyser le contenu du rapport
            print("\n🔍 PHASE 4: ANALYSE DU CONTENU DU RAPPORT")
            print("-" * 50)
            
            with open(rapport_file, 'r', encoding='utf-8') as f:
                contenu_rapport = f.read()
            
            # Rechercher la section de prédictibilité
            lignes = contenu_rapport.split('\n')
            section_predictibilite_trouvee = False
            section_contenu = []
            
            for i, ligne in enumerate(lignes):
                if "PRÉDICTIBILITÉ INDEX3 PAR INDEX1" in ligne:
                    section_predictibilite_trouvee = True
                    # Extraire les 20 lignes suivantes
                    section_contenu = lignes[i:i+20]
                    break
            
            if section_predictibilite_trouvee:
                print("✅ Section de prédictibilité trouvée dans le rapport")
                print("\nContenu de la section :")
                print("=" * 50)
                for ligne in section_contenu:
                    if ligne.strip():  # Ignorer les lignes vides
                        print(ligne)
                print("=" * 50)
                
                # Vérifier les éléments clés
                section_text = '\n'.join(section_contenu)
                
                elements_requis = {
                    'Score de prédictibilité': 'Score de prédictibilité :' in section_text,
                    'Réduction d\'incertitude': 'Réduction d\'incertitude :' in section_text,
                    'INDEX3 prédictible': 'INDEX3 prédictible :' in section_text,
                    'Information mutuelle': 'Information mutuelle :' in section_text
                }
                
                print(f"\n📋 ÉLÉMENTS VÉRIFIÉS :")
                for element, present in elements_requis.items():
                    print(f"   {element} : {'✅' if present else '❌'}")
                
                tous_presents = all(elements_requis.values())
                
                if tous_presents:
                    print("\n🎉 VALIDATION COMPLÈTE : Tous les éléments requis sont présents")
                    
                    # Extraire les valeurs numériques
                    import re
                    
                    # Rechercher l'information mutuelle
                    mi_match = re.search(r'Information mutuelle : ([\d.]+)', section_text)
                    if mi_match:
                        mi_rapport = float(mi_match.group(1))
                        mi_calcule = resultats_pred.get('information_mutuelle', 0)
                        
                        print(f"\n📊 VÉRIFICATION DES VALEURS :")
                        print(f"   Information mutuelle rapport : {mi_rapport:.6f}")
                        print(f"   Information mutuelle calculée : {mi_calcule:.6f}")
                        print(f"   Différence : {abs(mi_rapport - mi_calcule):.2e}")
                        
                        coherence_valeurs = abs(mi_rapport - mi_calcule) < 1e-6
                        print(f"   Cohérence : {'✅' if coherence_valeurs else '❌'}")
                    
                    return True
                else:
                    print("\n⚠️ VALIDATION PARTIELLE : Certains éléments manquent")
                    return False
            else:
                print("❌ Section de prédictibilité NON TROUVÉE dans le rapport")
                print("\nContenu du rapport (100 premières lignes) :")
                print("-" * 50)
                for i, ligne in enumerate(lignes[:100]):
                    print(f"{i+1:3d}: {ligne}")
                print("-" * 50)
                return False
                
        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(tmp_filename):
                os.unlink(tmp_filename)
        
    except Exception as e:
        print(f"❌ ERREUR : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DU RAPPORT")
    print("=" * 35)
    
    succes = generer_rapport_predictibilite()
    
    print("\n" + "=" * 35)
    if succes:
        print("✅ TEST RÉUSSI")
        print("Le rapport contient correctement l'analyse de prédictibilité avec formules exactes.")
    else:
        print("❌ TEST ÉCHOUÉ")
        print("Le rapport ne contient pas tous les éléments requis.")
    
    sys.exit(0 if succes else 1)
