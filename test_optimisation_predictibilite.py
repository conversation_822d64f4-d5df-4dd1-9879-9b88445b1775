#!/usr/bin/env python3
"""
TEST DES OPTIMISATIONS DE PRÉDICTIBILITÉ
=========================================

Ce script teste les optimisations ajoutées pour éviter le blocage
dans l'analyse de prédictibilité INDEX3 par INDEX1.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import time

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_optimisations_predictibilite():
    """
    Test des optimisations de prédictibilité
    """
    print("🔬 TEST DES OPTIMISATIONS DE PRÉDICTIBILITÉ")
    print("=" * 50)
    
    try:
        # 1. CRÉER UN ANALYSEUR AVEC DONNÉES DE TEST
        print("\n📊 PHASE 1: CRÉATION DE L'ANALYSEUR")
        print("-" * 40)
        
        from analyseur import AnalyseurSequencesLupasco
        
        # Utiliser le dataset existant
        analyseur = AnalyseurSequencesLupasco("dataset_test_3_parties_complet.json")
        analyseur.charger_donnees()
        
        print(f"   ✅ Analyseur créé et données chargées")
        
        # 2. TESTER L'ANALYSE DE PRÉDICTIBILITÉ AVEC OPTIMISATIONS
        print("\n📊 PHASE 2: TEST ANALYSE PRÉDICTIBILITÉ OPTIMISÉE")
        print("-" * 55)
        
        # Mesurer le temps d'exécution
        debut = time.time()
        
        try:
            # Tester avec use_advanced=False (rapide)
            print("   🔄 Test avec calculs traditionnels (use_advanced=False)...")
            resultats_simple = analyseur.analyser_predictibilite_index3_par_index1(use_advanced=False)
            
            temps_simple = time.time() - debut
            print(f"   ✅ Calculs traditionnels terminés en {temps_simple:.2f}s")
            
            # Tester avec use_advanced=None (auto-détection)
            print("   🔄 Test avec auto-détection des calculs avancés...")
            debut_auto = time.time()
            
            resultats_auto = analyseur.analyser_predictibilite_index3_par_index1(use_advanced=None)
            
            temps_auto = time.time() - debut_auto
            print(f"   ✅ Auto-détection terminée en {temps_auto:.2f}s")
            
            # Vérifier les résultats
            if 'erreur' in resultats_simple:
                print(f"   ❌ Erreur dans calculs traditionnels : {resultats_simple['erreur']}")
                return False
            
            if 'erreur' in resultats_auto:
                print(f"   ❌ Erreur dans auto-détection : {resultats_auto['erreur']}")
                return False
            
            # Afficher quelques métriques
            print(f"   📊 Score prédictibilité (traditionnel) : {resultats_simple['score_predictibilite']:.6f}")
            print(f"   📊 Score prédictibilité (auto) : {resultats_auto['score_predictibilite']:.6f}")
            print(f"   📊 Information mutuelle : {resultats_simple['information_mutuelle']:.6f}")
            
            # Vérifier que les optimisations ont été appliquées
            if resultats_auto.get('use_advanced', True) == False:
                print(f"   ✅ Optimisation appliquée : calculs avancés désactivés automatiquement")
            else:
                print(f"   ℹ️ Calculs avancés maintenus (dataset de taille acceptable)")
            
        except Exception as e:
            print(f"   ❌ Erreur dans l'analyse de prédictibilité : {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 3. TESTER AVEC TIMEOUT
        print("\n📊 PHASE 3: TEST AVEC TIMEOUT")
        print("-" * 30)
        
        # Tester avec un timeout pour s'assurer qu'il n'y a pas de blocage
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Timeout atteint")
        
        # Configurer un timeout de 60 secondes
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(60)
        
        try:
            print("   🔄 Test avec timeout de 60 secondes...")
            debut_timeout = time.time()
            
            # Forcer les calculs avancés pour tester les optimisations
            resultats_timeout = analyseur.analyser_predictibilite_index3_par_index1(use_advanced=True)
            
            temps_timeout = time.time() - debut_timeout
            signal.alarm(0)  # Annuler le timeout
            
            print(f"   ✅ Test timeout réussi en {temps_timeout:.2f}s")
            
            if 'erreur' not in resultats_timeout:
                print(f"   ✅ Calculs avancés terminés sans blocage")
            else:
                print(f"   ⚠️ Calculs avancés avec erreur (mais pas de blocage) : {resultats_timeout.get('erreur', 'Erreur inconnue')}")
            
        except TimeoutError:
            signal.alarm(0)
            print(f"   ❌ TIMEOUT : L'analyse a pris plus de 60 secondes")
            print(f"   🔧 Les optimisations doivent être renforcées")
            return False
        except Exception as e:
            signal.alarm(0)
            print(f"   ❌ Erreur dans le test timeout : {e}")
            return False
        
        # 4. RÉSUMÉ FINAL
        print("\n🏆 PHASE 4: RÉSUMÉ FINAL")
        print("-" * 25)
        
        print("   ✅ Analyse de prédictibilité optimisée")
        print("   ✅ Auto-détection des calculs avancés fonctionnelle")
        print("   ✅ Pas de blocage détecté")
        print("   ✅ Temps d'exécution acceptable")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR DANS LE TEST : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DES OPTIMISATIONS")
    print("=" * 45)
    
    succes = test_optimisations_predictibilite()
    
    print("\n" + "=" * 45)
    if succes:
        print("✅ TEST RÉUSSI - OPTIMISATIONS FONCTIONNELLES")
        print("L'analyse de prédictibilité ne bloque plus.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ - OPTIMISATIONS INSUFFISANTES")
        print("Des améliorations supplémentaires sont nécessaires.")
        sys.exit(1)
