#!/usr/bin/env python3
"""
TEST DE VÉRIFICATION DES FORMULES EXACTES DANS L'ANALYSE DE PRÉDICTIBILITÉ
=========================================================================

Ce script vérifie que l'analyse de prédictibilité INDEX3 par INDEX1 utilise bien
les formules mathématiques exactes consolidées.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_utilisation_formules_exactes():
    """
    Test principal pour vérifier l'utilisation des formules exactes
    """
    print("🔬 VÉRIFICATION DES FORMULES EXACTES DANS L'ANALYSE DE PRÉDICTIBILITÉ")
    print("=" * 75)
    
    try:
        # 1. Vérifier l'import des formules exactes
        print("\n📊 PHASE 1: VÉRIFICATION DES IMPORTS")
        print("-" * 45)
        
        # Import des formules consolidées
        from formules_mathematiques_exactes import (
            information_mutuelle_exacte,
            entropie_conditionnelle_exacte
        )
        print("✅ Formules exactes consolidées importées")
        
        # Import des formules dans math_formulas
        from lupasco_refactored.utils.math_formulas import (
            information_mutuelle_corrigee,
            entropie_conditionnelle_corrigee
        )
        print("✅ Formules corrigées dans math_formulas importées")
        
        # 2. Test de cohérence entre les formules
        print("\n🔬 PHASE 2: TEST DE COHÉRENCE ENTRE FORMULES")
        print("-" * 50)
        
        # Données de test
        np.random.seed(42)
        index1_test = np.random.choice(['SYNC', 'DESYNC'], 1000).tolist()
        index3_test = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 1000).tolist()
        
        # Calculer avec les formules exactes consolidées
        mi_exacte = information_mutuelle_exacte(index1_test, index3_test)
        ec_exacte = entropie_conditionnelle_exacte(index3_test, index1_test)
        
        # Calculer avec les formules corrigées
        mi_corrigee = information_mutuelle_corrigee(index1_test, index3_test)
        ec_corrigee = entropie_conditionnelle_corrigee(index3_test, index1_test)
        
        # Vérifier la cohérence
        diff_mi = abs(mi_exacte - mi_corrigee)
        diff_ec = abs(ec_exacte - ec_corrigee)
        
        print(f"Information mutuelle exacte    : {mi_exacte:.6f}")
        print(f"Information mutuelle corrigée  : {mi_corrigee:.6f}")
        print(f"Différence MI                  : {diff_mi:.2e}")
        
        print(f"Entropie conditionnelle exacte : {ec_exacte:.6f}")
        print(f"Entropie conditionnelle corrigée: {ec_corrigee:.6f}")
        print(f"Différence EC                  : {diff_ec:.2e}")
        
        coherence_mi = diff_mi < 1e-10
        coherence_ec = diff_ec < 1e-10
        
        print(f"Cohérence MI : {'✅' if coherence_mi else '❌'}")
        print(f"Cohérence EC : {'✅' if coherence_ec else '❌'}")
        
        # 3. Test de l'analyseur de prédictibilité
        print("\n🎯 PHASE 3: TEST DE L'ANALYSEUR DE PRÉDICTIBILITÉ")
        print("-" * 55)
        
        # Créer des séquences de test
        sequences_test = {
            'INDEX1': index1_test,
            'INDEX3': index3_test,
            'INDEX1_INDEX3': [f"{i1}_{i3}" for i1, i3 in zip(index1_test, index3_test)]
        }
        
        # Tester l'analyseur Lupasco
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
        
        analyzer = LupascoAnalyzer(sequences_test)
        
        # Capturer la sortie pour vérifier le message
        import io
        from contextlib import redirect_stdout
        
        captured_output = io.StringIO()
        
        with redirect_stdout(captured_output):
            resultats = analyzer.analyser_predictibilite_index3_par_index1()
        
        output_text = captured_output.getvalue()
        
        # Vérifier que le message des formules exactes apparaît
        formules_exactes_utilisees = "✅ Utilisation des formules exactes pour l'information mutuelle" in output_text
        
        print(f"Message formules exactes détecté : {'✅' if formules_exactes_utilisees else '❌'}")
        
        if formules_exactes_utilisees:
            print("✅ L'analyseur utilise bien les formules exactes")
        else:
            print("⚠️ L'analyseur pourrait ne pas utiliser les formules exactes")
            print("Sortie capturée :")
            print(output_text)
        
        # Vérifier les résultats
        if 'information_mutuelle' in resultats:
            mi_analyseur = resultats['information_mutuelle']
            print(f"Information mutuelle de l'analyseur : {mi_analyseur:.6f}")
            
            # Comparer avec nos calculs
            diff_analyseur = abs(mi_analyseur - mi_exacte)
            print(f"Différence avec formule exacte : {diff_analyseur:.2e}")
            
            coherence_analyseur = diff_analyseur < 1e-6  # Tolérance plus large
            print(f"Cohérence analyseur : {'✅' if coherence_analyseur else '❌'}")
        else:
            print("❌ Information mutuelle non trouvée dans les résultats")
            coherence_analyseur = False
        
        # 4. Test du rapport
        print("\n📝 PHASE 4: TEST DU RAPPORT")
        print("-" * 30)
        
        # Simuler la génération de rapport
        from lupasco_refactored.reporting.report_generator import ReportGenerator
        
        resultats_complets = {
            'PREDICTIBILITE_INDEX3_PAR_INDEX1': resultats
        }
        
        # Créer un fichier temporaire pour le rapport
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_filename = tmp_file.name
        
        try:
            report_gen = ReportGenerator(
                resultats=resultats_complets,
                sequences=sequences_test,
                fichier_json="test.json",
                nb_parties_total=100
            )
            
            rapport_file = report_gen.generer_rapport(tmp_filename)
            
            # Lire le rapport et vérifier le contenu
            with open(rapport_file, 'r', encoding='utf-8') as f:
                contenu_rapport = f.read()
            
            # Vérifier que la section de prédictibilité est présente
            section_predictibilite = "PRÉDICTIBILITÉ INDEX3 PAR INDEX1" in contenu_rapport
            information_mutuelle_rapport = "Information mutuelle :" in contenu_rapport
            
            print(f"Section prédictibilité présente : {'✅' if section_predictibilite else '❌'}")
            print(f"Information mutuelle dans rapport : {'✅' if information_mutuelle_rapport else '❌'}")
            
            if section_predictibilite and information_mutuelle_rapport:
                print("✅ Le rapport contient bien l'analyse de prédictibilité avec information mutuelle")
            else:
                print("⚠️ Le rapport pourrait être incomplet")
            
        finally:
            # Nettoyer le fichier temporaire
            if os.path.exists(tmp_filename):
                os.unlink(tmp_filename)
        
        # 5. Conclusion
        print("\n🏆 CONCLUSION")
        print("-" * 15)
        
        score_global = (
            coherence_mi * 0.25 +
            coherence_ec * 0.25 +
            formules_exactes_utilisees * 0.25 +
            coherence_analyseur * 0.25
        )
        
        print(f"Score global : {score_global:.1%}")
        
        if score_global >= 0.8:
            print("🎉 VALIDATION RÉUSSIE : Les formules exactes sont bien utilisées")
            return True
        else:
            print("⚠️ VALIDATION PARTIELLE : Quelques problèmes détectés")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DE VÉRIFICATION")
    print("=" * 40)
    
    succes = test_utilisation_formules_exactes()
    
    print("\n" + "=" * 40)
    if succes:
        print("✅ TEST RÉUSSI")
        print("Les formules exactes sont correctement utilisées dans l'analyse de prédictibilité.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ")
        print("Des problèmes ont été détectés dans l'utilisation des formules exactes.")
        sys.exit(1)
