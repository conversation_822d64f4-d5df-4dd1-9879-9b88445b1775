"""
Module d'analyse spécialisée Lupasco

Ce module contient les analyses spécialisées selon la logique de Lupasco :
- Transitions DESYNC/SYNC
- Cycles de période 2 et 3
- Prédictibilité INDEX3 par INDEX1
- Analyse Lupasco par parties

Auteur: Assistant IA
Date: 2025-06-19
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Any, Optional
from collections import Counter
import math

# Imports optionnels pour corrections avancées
try:
    from scipy.stats import spearmanr, chi2_contingency
    HAS_SCIPY_STATS = True
except ImportError:
    HAS_SCIPY_STATS = False

# Imports des modules refactorisés
from lupasco_refactored.utils.data_utils import nettoyer_marqueurs, diviser_en_parties, calculer_entropie_shannon
from lupasco_refactored.statistics.basic_stats import BasicStatistics
from lupasco_refactored.statistics.transitions import TransitionAnalyzer
from lupasco_refactored.statistics.entropy_advanced import AdvancedEntropy
from lupasco_refactored.core.sequence_extractor import SequenceExtractor

# Imports avec fallback pour les formules mathématiques
try:
    from formules_mathematiques_exactes import (
        gini_coefficient, coefficient_of_variation, autocorrelation_function,
        runs_test, calculer_entropie_locale, lupasco_entropy_analysis,
        z_score, detect_anomalies, lupasco_statistical_summary
    )
except ImportError:
    # Fallback si le module n'est pas disponible
    def gini_coefficient(data):
        return 0.5
    def coefficient_of_variation(data):
        return 0.5
    def autocorrelation_function(data, max_lag=1):
        return [0.0] * (max_lag + 1)
    def runs_test(data):
        return 0.5
    def calculer_entropie_locale(sequence, combinaison):
        return 0.5
    def lupasco_entropy_analysis(index1, index2, index3):
        return {'erreur': 'Module formules_mathematiques_exactes non disponible'}
    def z_score(data):
        return np.zeros(len(data)) if len(data) > 0 else []
    def detect_anomalies(data, threshold=2.0):
        return {'anomalies_indices': np.array([]), 'z_scores': np.array([]), 'threshold': threshold}
    def lupasco_statistical_summary(data_dict):
        return pd.DataFrame()


class LupascoAnalyzer:
    """
    Analyseur spécialisé pour les analyses Lupasco
    
    Cette classe regroupe toutes les analyses spécialisées selon la logique de Lupasco :
    - Transitions DESYNC/SYNC
    - Cycles de période 2 et 3  
    - Prédictibilité INDEX3 par INDEX1
    - Analyse Lupasco par parties
    """
    
    def __init__(self, sequences: Dict[str, List[str]]):
        """
        Initialise l'analyseur Lupasco

        Args:
            sequences: Dictionnaire des séquences {nom_index: sequence}
        """
        self.sequences = sequences
        self.resultats = {}

        # Initialiser les modules utilitaires
        self.stats = BasicStatistics()
        self.transition_analyzer = TransitionAnalyzer()
        self.entropy_analyzer = AdvancedEntropy()
        self.sequence_extractor = SequenceExtractor(sequences)

        # Configuration des corrections avancées
        self.ADAPTIVE_THRESHOLDS = {
            'small': (1000, 0.85),    # n < 1000 : seuil 0.85
            'medium': (10000, 0.75),  # 1000 <= n < 10000 : seuil 0.75
            'large': (float('inf'), 0.65)  # n >= 10000 : seuil 0.65
        }
        self.PERMUTATION_ITERATIONS = {
            'min': 100, 'max': 1000, 'factor': 100  # min(max, max(min, n//factor))
        }
    
    def analyser_transitions_desync_sync(self) -> Dict[str, Any]:
        """
        Analyse les transitions DESYNC→SYNC et SYNC→DESYNC dans INDEX1
        
        Returns:
            dict: Résultats de l'analyse des transitions DESYNC/SYNC
        """
        print("\n🔬 ANALYSE DES TRANSITIONS DESYNC→SYNC")
        print("=" * 45)
        
        # Extraire INDEX1 (SYNC/DESYNC)
        if 'INDEX1' not in self.sequences:
            print("   ❌ INDEX1 non disponible")
            return {'erreur': 'INDEX1 non disponible'}
        
        index1_sequence = nettoyer_marqueurs(self.sequences['INDEX1'])
        
        # Compter les transitions
        transitions_desync_sync = 0
        transitions_sync_desync = 0
        transitions_totales = 0
        
        for i in range(len(index1_sequence) - 1):
            etat_actuel = index1_sequence[i]
            etat_suivant = index1_sequence[i + 1]
            
            if etat_actuel == 'DESYNC' and etat_suivant == 'SYNC':
                transitions_desync_sync += 1
            elif etat_actuel == 'SYNC' and etat_suivant == 'DESYNC':
                transitions_sync_desync += 1
            
            transitions_totales += 1
        
        # Calculer les pourcentages
        pct_desync_sync = (transitions_desync_sync / transitions_totales * 100) if transitions_totales > 0 else 0
        pct_sync_desync = (transitions_sync_desync / transitions_totales * 100) if transitions_totales > 0 else 0
        
        print(f"   Transitions totales : {transitions_totales:,}")
        print(f"   DESYNC→SYNC : {transitions_desync_sync:,} ({pct_desync_sync:.2f}%)")
        print(f"   SYNC→DESYNC : {transitions_sync_desync:,} ({pct_sync_desync:.2f}%)")
        
        # Analyse statistique des transitions
        sequence_binaire = [1 if x == 'DESYNC' else 0 for x in index1_sequence]
        runs_result = runs_test(sequence_binaire)
        
        resultats = {
            'transitions_totales': transitions_totales,
            'desync_vers_sync': {
                'total': transitions_desync_sync,
                'pourcentage': pct_desync_sync
            },
            'sync_vers_desync': {
                'total': transitions_sync_desync,
                'pourcentage': pct_sync_desync
            },
            'runs_test_p_value': runs_result,
            'sequence_aleatoire': runs_result > 0.05,
            'equilibre_transitions': abs(pct_desync_sync - pct_sync_desync) < 5.0
        }
        
        self.resultats['TRANSITIONS_DESYNC_SYNC'] = resultats
        return resultats
    
    def analyser_cycles_periode_2_et_3(self) -> Dict[str, Any]:
        """
        Analyse les cycles de période 2 et 3 dans toutes les séquences
        
        Returns:
            dict: Résultats de l'analyse des cycles de période 2 et 3
        """
        print("\n🔬 ANALYSE DES CYCLES DE PÉRIODE 2 ET 3")
        print("=" * 45)
        
        resultats = {}
        
        for nom_sequence, sequence_brute in self.sequences.items():
            if nom_sequence in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
                print(f"\n   📊 Analyse {nom_sequence}")
                
                sequence = nettoyer_marqueurs(sequence_brute)
                
                # Détecter cycles de période 2
                cycles_2 = self._detecter_cycles_periode_n(sequence, 2)
                
                # Détecter cycles de période 3
                cycles_3 = self._detecter_cycles_periode_n(sequence, 3)
                
                resultats[nom_sequence] = {
                    'cycles_periode_2': cycles_2,
                    'cycles_periode_3': cycles_3,
                    'total_cycles_detectes': cycles_2['nb_cycles'] + cycles_3['nb_cycles']
                }
                
                print(f"      Cycles période 2 : {cycles_2['nb_cycles']}")
                print(f"      Cycles période 3 : {cycles_3['nb_cycles']}")
        
        self.resultats['CYCLES_PERIODE_2_ET_3'] = resultats
        return resultats
    
    def _detecter_cycles_periode_n(self, sequence: List[str], periode: int) -> Dict[str, Any]:
        """
        Détecte les cycles de période n dans une séquence
        
        Args:
            sequence: Séquence à analyser
            periode: Période du cycle à détecter
            
        Returns:
            dict: Informations sur les cycles détectés
        """
        cycles_detectes = []
        nb_cycles = 0
        
        # Parcourir la séquence pour détecter les répétitions
        for i in range(len(sequence) - periode * 2 + 1):
            pattern = sequence[i:i + periode]
            pattern_suivant = sequence[i + periode:i + periode * 2]
            
            if pattern == pattern_suivant:
                cycles_detectes.append({
                    'position': i,
                    'pattern': pattern,
                    'longueur': periode
                })
                nb_cycles += 1
        
        return {
            'nb_cycles': nb_cycles,
            'cycles_detectes': cycles_detectes[:10],  # Limiter à 10 exemples
            'densite_cycles': nb_cycles / len(sequence) if len(sequence) > 0 else 0
        }

    def analyser_predictibilite_index3_par_index1(self, use_advanced: bool = True) -> Dict[str, Any]:
        """
        Analyse la prédictibilité d'INDEX3 par INDEX1

        Returns:
            dict: Résultats de l'analyse de prédictibilité
        """
        print("\n🔬 ANALYSE PRÉDICTIBILITÉ INDEX3 PAR INDEX1")
        print("=" * 50)

        # Vérifier la disponibilité des séquences
        if 'INDEX1' not in self.sequences or 'INDEX3' not in self.sequences:
            print("   ❌ INDEX1 ou INDEX3 non disponible")
            return {'erreur': 'INDEX1 ou INDEX3 non disponible'}

        index1_sequence = nettoyer_marqueurs(self.sequences['INDEX1'])
        index3_sequence = nettoyer_marqueurs(self.sequences['INDEX3'])

        if len(index1_sequence) != len(index3_sequence):
            print("   ❌ Tailles des séquences INDEX1 et INDEX3 différentes")
            return {'erreur': 'Tailles des séquences différentes'}

        # Calculer les probabilités conditionnelles P(INDEX3|INDEX1)
        prob_conditionnelles = self._calculer_probabilites_conditionnelles(index1_sequence, index3_sequence)

        # Calculer l'entropie conditionnelle H(INDEX3|INDEX1) et information mutuelle avec formules exactes
        h_index3 = calculer_entropie_shannon(index3_sequence)

        # Essayer d'utiliser les formules exactes d'abord
        try:
            from lupasco_refactored.utils.math_formulas import information_mutuelle_corrigee, entropie_conditionnelle_corrigee

            # Utiliser les formules exactes
            mutual_info = information_mutuelle_corrigee(index1_sequence, index3_sequence)
            h_conditional = entropie_conditionnelle_corrigee(index3_sequence, index1_sequence)

            print("   ✅ Utilisation des formules exactes pour l'information mutuelle")

        except ImportError as e:
            print(f"   ⚠️ Formules exactes non disponibles, utilisation méthode manuelle : {e}")
            # Fallback vers méthode manuelle
            h_conditional = self._entropie_conditionnelle(index3_sequence, index1_sequence)
            mutual_info = h_index3 - h_conditional

        # Score de prédictibilité avec corrections avancées
        n_samples = len(index1_sequence)

        if use_advanced:
            # Score brut traditionnel
            predictability_score_raw = h_conditional / h_index3 if h_index3 > 0 else 1

            # Corrections avancées
            adaptive_threshold_info = self._calculate_adaptive_threshold(n_samples)
            nonlinear_measures = self._calculate_nonlinear_measures(index1_sequence, index3_sequence)
            bias_correction = self._correct_small_sample_bias(predictability_score_raw, index1_sequence, index3_sequence)
            statistical_tests = self._perform_statistical_tests(index1_sequence, index3_sequence, mutual_info)

            # Score final corrigé
            predictability_score = bias_correction['score_corrected']
            adaptive_threshold = adaptive_threshold_info['threshold']
        else:
            # Méthode traditionnelle
            predictability_score = h_conditional / h_index3 if h_index3 > 0 else 1
            adaptive_threshold = 0.9
            nonlinear_measures = {}
            bias_correction = {}
            statistical_tests = {}
            adaptive_threshold_info = {}

        print(f"   Entropie H(INDEX3) : {h_index3:.6f} bits")
        print(f"   Entropie conditionnelle H(INDEX3|INDEX1) : {h_conditional:.6f} bits")
        print(f"   Information mutuelle I(INDEX1;INDEX3) : {mutual_info:.6f} bits")
        print(f"   Score de prédictibilité : {predictability_score:.6f}")
        print(f"   Réduction d'incertitude : {(1-predictability_score)*100:.2f}%")

        if use_advanced:
            print(f"   Seuil adaptatif : {adaptive_threshold:.3f} (taille: {adaptive_threshold_info['category']})")
            print(f"   Mesures non-linéaires : Cramér V={nonlinear_measures['cramer_v']:.3f}, Score composite={nonlinear_measures['composite_score']:.3f}")
            if bias_correction.get('correction_applied', False):
                print(f"   Correction biais : {bias_correction['bias_estimate']:.4f} (appliquée)")
            print(f"   Tests statistiques : p-value={statistical_tests['p_value']:.3e}, significatif={'Oui' if statistical_tests['significant'] else 'Non'}")

        if predictability_score < adaptive_threshold:
            if use_advanced and statistical_tests.get('significant', False):
                print(f"   ✅ INDEX3 est PRÉDICTIBLE par INDEX1 (statistiquement significatif)")
            elif use_advanced:
                print(f"   ⚠️ INDEX3 est PRÉDICTIBLE par INDEX1 (non significatif)")
            else:
                print(f"   ✅ INDEX3 est PRÉDICTIBLE par INDEX1")
        else:
            print(f"   ❌ INDEX3 est PEU PRÉDICTIBLE par INDEX1")

        resultats = {
            'probabilites_conditionnelles': prob_conditionnelles,
            'entropie_index3': float(h_index3),
            'entropie_conditionnelle': float(h_conditional),
            'information_mutuelle': float(mutual_info),
            'score_predictibilite': float(predictability_score),
            'reduction_incertitude_pct': float((1-predictability_score)*100),
            'predictible': bool(predictability_score < adaptive_threshold),
            'use_advanced': use_advanced,
            'taille_echantillon': n_samples
        }

        # Ajouter les résultats avancés si utilisés
        if use_advanced:
            resultats.update({
                'score_predictibilite_raw': float(bias_correction.get('score_original', predictability_score)),
                'seuil_adaptatif': float(adaptive_threshold),
                'seuil_adaptatif_info': adaptive_threshold_info,
                'mesures_nonlineaires': nonlinear_measures,
                'correction_biais': bias_correction,
                'tests_statistiques': statistical_tests,
                'predictible_significatif': bool(
                    predictability_score < adaptive_threshold and
                    statistical_tests.get('significant', False)
                ),
                'methode': 'predictability_advanced_v1'
            })
        else:
            resultats.update({
                'seuil_utilise': 0.9,
                'methode': 'predictability_traditional'
            })

        self.resultats['PREDICTIBILITE_INDEX3_PAR_INDEX1'] = resultats
        return resultats

    def analyser_lupasco_par_parties(self, nb_parties_echantillon: int = 100) -> Dict[str, Any]:
        """
        Analyse Lupasco sur un échantillon de parties indépendantes

        Args:
            nb_parties_echantillon: Nombre de parties à analyser

        Returns:
            dict: Résultats de l'analyse Lupasco par parties
        """
        print(f"\n🔬 ANALYSE LUPASCO PAR PARTIES (échantillon de {nb_parties_echantillon} parties)")
        print("=" * 70)

        # Vérifier la disponibilité d'INDEX5
        if 'INDEX5' not in self.sequences:
            print("   ❌ INDEX5 non disponible")
            return {'erreur': 'INDEX5 non disponible'}

        # Diviser INDEX5 en parties
        index5_brute = self.sequences['INDEX5']
        parties_index5 = diviser_en_parties(index5_brute)

        print(f"   📊 Parties disponibles : {len(parties_index5)}")

        # Limiter à l'échantillon demandé
        nb_parties_analyse = min(nb_parties_echantillon, len(parties_index5))
        parties_echantillon = parties_index5[:nb_parties_analyse]

        print(f"   📊 Parties analysées : {nb_parties_analyse}")

        # Analyser chaque partie
        resultats_parties = []

        for i, partie in enumerate(parties_echantillon):
            if i % 10 == 0:  # Affichage du progrès
                print(f"   🔄 Analyse partie {i+1}/{nb_parties_analyse}")

            # Extraire les composants INDEX1, INDEX2, INDEX3 de cette partie
            # Créer un dictionnaire temporaire avec cette partie
            sequences_partie = {'INDEX5': partie}
            extractor_partie = SequenceExtractor(sequences_partie)
            composants = extractor_partie.extraire_composants_index5()

            if composants and all(composants.values()):
                # Analyse Lupasco pour cette partie
                analyse_partie = self._analyser_partie_lupasco(composants, i+1)
                resultats_parties.append(analyse_partie)

        # Synthèse des résultats
        synthese = self._synthetiser_resultats_parties(resultats_parties)

        resultats = {
            'nb_parties_analysees': len(resultats_parties),
            'nb_parties_disponibles': len(parties_index5),
            'resultats_parties': resultats_parties[:10],  # Limiter à 10 exemples
            'synthese': synthese
        }

        self.resultats['LUPASCO_PAR_PARTIES'] = resultats
        return resultats

    def _calculer_probabilites_conditionnelles(self, x_data: List[str], y_data: List[str]) -> Dict[str, Dict[str, float]]:
        """
        Calcule les probabilités conditionnelles P(Y|X)

        Args:
            x_data: Séquence conditionnante (INDEX1)
            y_data: Séquence conditionnée (INDEX3)

        Returns:
            dict: Probabilités conditionnelles {x_val: {y_val: prob}}
        """
        prob_conditionnelles = {}

        # Grouper par valeur de X
        x_values = set(x_data)

        for x_val in x_values:
            # Filtrer Y pour cette valeur de X
            y_given_x = [y_data[i] for i in range(len(y_data)) if x_data[i] == x_val]

            if y_given_x:
                # Compter les occurrences de Y|X
                y_counts = Counter(y_given_x)
                total_y_given_x = len(y_given_x)

                # Calculer les probabilités
                prob_conditionnelles[x_val] = {
                    y_val: count / total_y_given_x
                    for y_val, count in y_counts.items()
                }

        return prob_conditionnelles

    def _entropie_conditionnelle(self, y_data: List[str], x_data: List[str]) -> float:
        """
        Calcule l'entropie conditionnelle H(Y|X)

        Args:
            y_data: Séquence Y
            x_data: Séquence X

        Returns:
            float: Entropie conditionnelle
        """
        if not y_data or not x_data or len(y_data) != len(x_data):
            return 0.0

        x_counts = Counter(x_data)
        total = len(y_data)
        h_conditional = 0.0

        # Pour chaque valeur de X
        for x_val in x_counts:
            # Filtrer Y pour cette valeur de X
            y_given_x = [y_data[i] for i in range(len(y_data)) if x_data[i] == x_val]

            if y_given_x:
                p_x = len(y_given_x) / total
                y_counts = Counter(y_given_x)
                total_y_given_x = len(y_given_x)

                # Calculer H(Y|X=x_val)
                h_y_given_x = 0.0
                for y_val, count in y_counts.items():
                    if count > 0:
                        p_y_given_x = count / total_y_given_x
                        h_y_given_x -= p_y_given_x * math.log2(p_y_given_x)

                h_conditional += p_x * h_y_given_x

        return h_conditional

    def _analyser_partie_lupasco(self, composants: Dict[str, List[str]], numero_partie: int) -> Dict[str, Any]:
        """
        Analyse Lupasco pour une partie spécifique

        Args:
            composants: Composants INDEX1, INDEX2, INDEX3 de la partie
            numero_partie: Numéro de la partie

        Returns:
            dict: Résultats de l'analyse Lupasco pour cette partie
        """
        try:
            # Analyse entropique Lupasco
            analyse_lupasco = lupasco_entropy_analysis(
                composants['INDEX1'],
                composants['INDEX2'],
                composants['INDEX3']
            )

            # Statistiques de base
            nb_elements = len(composants['INDEX1'])

            return {
                'numero_partie': numero_partie,
                'nb_elements': nb_elements,
                'analyse_lupasco': analyse_lupasco,
                'erreur': None
            }

        except Exception as e:
            return {
                'numero_partie': numero_partie,
                'nb_elements': len(composants.get('INDEX1', [])),
                'analyse_lupasco': {},
                'erreur': str(e)
            }

    def _synthetiser_resultats_parties(self, resultats_parties: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Synthétise les résultats de toutes les parties analysées

        Args:
            resultats_parties: Liste des résultats par partie

        Returns:
            dict: Synthèse des résultats
        """
        if not resultats_parties:
            return {'erreur': 'Aucune partie analysée'}

        # Compter les parties avec/sans erreur
        parties_valides = [r for r in resultats_parties if r['erreur'] is None]
        parties_erreur = [r for r in resultats_parties if r['erreur'] is not None]

        # Calculer les moyennes des métriques Lupasco
        entropies_moyennes = {}
        if parties_valides:
            # Extraire les entropies de toutes les parties valides
            for partie in parties_valides:
                analyse = partie.get('analyse_lupasco', {})
                for key, value in analyse.items():
                    if isinstance(value, (int, float)) and not math.isnan(value):
                        if key not in entropies_moyennes:
                            entropies_moyennes[key] = []
                        entropies_moyennes[key].append(value)

            # Calculer les moyennes
            moyennes = {
                key: np.mean(values) for key, values in entropies_moyennes.items()
            }
        else:
            moyennes = {}

        return {
            'nb_parties_valides': len(parties_valides),
            'nb_parties_erreur': len(parties_erreur),
            'taux_reussite': len(parties_valides) / len(resultats_parties) * 100,
            'moyennes_metriques': moyennes,
            'nb_elements_total': sum(r['nb_elements'] for r in resultats_parties)
        }

    # ============================================================================
    # MÉTHODES PRIVÉES POUR CORRECTIONS AVANCÉES
    # ============================================================================

    def _calculate_adaptive_threshold(self, n_samples: int) -> Dict[str, Any]:
        """
        Calcule un seuil adaptatif basé sur la taille d'échantillon (VERSION SIMPLIFIÉE)

        Args:
            n_samples: Taille de l'échantillon

        Returns:
            dict: Informations sur le seuil adaptatif
        """
        # Seuil adaptatif simple selon la configuration
        if n_samples < self.ADAPTIVE_THRESHOLDS['small'][0]:
            threshold = self.ADAPTIVE_THRESHOLDS['small'][1]
            category = 'small'
        elif n_samples < self.ADAPTIVE_THRESHOLDS['medium'][0]:
            threshold = self.ADAPTIVE_THRESHOLDS['medium'][1]
            category = 'medium'
        else:
            threshold = self.ADAPTIVE_THRESHOLDS['large'][1]
            category = 'large'

        return {
            'threshold': threshold,
            'category': category,
            'n_samples': n_samples,
            'method': 'adaptive_simple'
        }

    def _get_bootstrap_iterations(self, n_samples: int) -> int:
        """Calcule le nombre d'itérations bootstrap adaptatif"""
        return min(1000, max(100, n_samples // 10))

    def _get_permutation_iterations(self, n_samples: int) -> int:
        """Calcule le nombre d'itérations permutation adaptatif"""
        return min(self.PERMUTATION_ITERATIONS['max'],
                  max(self.PERMUTATION_ITERATIONS['min'],
                      n_samples // self.PERMUTATION_ITERATIONS['factor']))

    def _calculate_nonlinear_measures(self, x_data: List[str], y_data: List[str]) -> Dict[str, float]:
        """
        Calcule les mesures de dépendance non-linéaire (VERSION SIMPLIFIÉE)

        Args:
            x_data: Variable X
            y_data: Variable Y

        Returns:
            dict: Mesures non-linéaires
        """
        measures = {
            'cramer_v': 0.0,
            'spearman_correlation': 0.0,
            'mutual_info_normalized': 0.0,
            'composite_score': 0.0
        }

        try:
            # 1. Coefficient V de Cramér (utilise chi2_contingency si disponible)
            measures['cramer_v'] = self._simple_cramer_v(x_data, y_data)

            # 2. Corrélation de Spearman (approximation pour variables discrètes)
            if HAS_SCIPY_STATS:
                measures['spearman_correlation'] = self._spearman_discrete(x_data, y_data)

            # 3. Information mutuelle normalisée
            measures['mutual_info_normalized'] = self._normalized_mutual_info(x_data, y_data)

            # 4. Score composite
            measures['composite_score'] = np.mean([
                measures['cramer_v'],
                abs(measures['spearman_correlation']),
                measures['mutual_info_normalized']
            ])

        except Exception as e:
            print(f"   ⚠️ Erreur calcul mesures non-linéaires : {e}")

        return measures

    def _simple_cramer_v(self, x_data: List[str], y_data: List[str]) -> float:
        """Calcule le coefficient V de Cramér simplifié"""
        try:
            # Construire table de contingence
            x_unique = sorted(set(x_data))
            y_unique = sorted(set(y_data))

            table = np.zeros((len(x_unique), len(y_unique)))

            for x, y in zip(x_data, y_data):
                i = x_unique.index(x)
                j = y_unique.index(y)
                table[i, j] += 1

            # Calculer chi² manuellement si scipy non disponible
            if HAS_SCIPY_STATS:
                chi2, _, _, _ = chi2_contingency(table)
            else:
                chi2 = self._manual_chi2(table)

            n = np.sum(table)
            min_dim = min(table.shape) - 1

            if n > 0 and min_dim > 0:
                return np.sqrt(chi2 / (n * min_dim))
            return 0.0

        except Exception:
            return 0.0

    def _manual_chi2(self, table: np.ndarray) -> float:
        """Calcule chi² manuellement"""
        row_totals = np.sum(table, axis=1)
        col_totals = np.sum(table, axis=0)
        total = np.sum(table)

        chi2 = 0.0
        for i in range(table.shape[0]):
            for j in range(table.shape[1]):
                expected = row_totals[i] * col_totals[j] / total
                if expected > 0:
                    chi2 += (table[i, j] - expected) ** 2 / expected

        return chi2

    def _spearman_discrete(self, x_data: List[str], y_data: List[str]) -> float:
        """Corrélation de Spearman pour variables discrètes"""
        try:
            # Convertir en rangs
            x_unique = sorted(set(x_data))
            y_unique = sorted(set(y_data))

            x_ranks = [x_unique.index(x) for x in x_data]
            y_ranks = [y_unique.index(y) for y in y_data]

            if len(set(x_ranks)) > 1 and len(set(y_ranks)) > 1:
                corr, _ = spearmanr(x_ranks, y_ranks)
                return corr if not np.isnan(corr) else 0.0
            return 0.0

        except Exception:
            return 0.0

    def _normalized_mutual_info(self, x_data: List[str], y_data: List[str]) -> float:
        """Information mutuelle normalisée"""
        try:
            from lupasco_refactored.utils.math_formulas import information_mutuelle_corrigee
            from lupasco_refactored.utils.data_utils import calculer_entropie_shannon

            mi = information_mutuelle_corrigee(x_data, y_data)
            h_x = calculer_entropie_shannon(x_data)
            h_y = calculer_entropie_shannon(y_data)

            if h_x > 0 and h_y > 0:
                return 2 * mi / (h_x + h_y)
            return 0.0

        except Exception:
            return 0.0

    def _correct_small_sample_bias(self, score_raw: float, x_data: List[str], y_data: List[str]) -> Dict[str, Any]:
        """
        Corrige le biais pour petits échantillons (VERSION SIMPLIFIÉE)

        Args:
            score_raw: Score de prédictibilité brut
            x_data: Variable X
            y_data: Variable Y

        Returns:
            dict: Score corrigé et informations
        """
        n_samples = len(x_data)

        # Correction Miller-Madow simple pour l'entropie
        correction_factor = self._miller_madow_correction(n_samples)

        # Bootstrap simplifié
        bootstrap_scores = []
        n_bootstrap = self._get_bootstrap_iterations(n_samples)

        try:
            for _ in range(min(n_bootstrap, 100)):  # Limiter pour performance
                # Échantillonnage bootstrap
                indices = np.random.choice(n_samples, n_samples, replace=True)
                x_boot = [x_data[i] for i in indices]
                y_boot = [y_data[i] for i in indices]

                # Calculer score bootstrap
                score_boot = self._calculate_predictability_score_simple(x_boot, y_boot)
                bootstrap_scores.append(score_boot)

            # Correction de biais
            if bootstrap_scores:
                score_bootstrap_mean = np.mean(bootstrap_scores)
                bias_estimate = score_bootstrap_mean - score_raw
                score_corrected = max(0, min(1, score_raw - bias_estimate))

                # Intervalle de confiance
                ic_inf = np.percentile(bootstrap_scores, 2.5)
                ic_sup = np.percentile(bootstrap_scores, 97.5)
            else:
                score_corrected = score_raw
                bias_estimate = 0.0
                ic_inf = ic_sup = score_raw

        except Exception as e:
            print(f"   ⚠️ Erreur bootstrap : {e}")
            score_corrected = score_raw
            bias_estimate = 0.0
            ic_inf = ic_sup = score_raw

        return {
            'score_original': score_raw,
            'score_corrected': score_corrected,
            'bias_estimate': bias_estimate,
            'miller_madow_correction': correction_factor,
            'confidence_interval_95': [ic_inf, ic_sup],
            'n_bootstrap': len(bootstrap_scores),
            'correction_applied': abs(bias_estimate) > 0.01
        }

    def _miller_madow_correction(self, n_samples: int) -> float:
        """Correction Miller-Madow pour l'entropie"""
        if n_samples <= 1:
            return 0.0
        # Correction approximative : (k-1)/(2n) où k est le nombre d'états
        # Utilisation d'une approximation conservative
        return min(0.1, 1.0 / (2 * n_samples))

    def _calculate_predictability_score_simple(self, x_data: List[str], y_data: List[str]) -> float:
        """Calcule le score de prédictibilité simple pour bootstrap"""
        try:
            from lupasco_refactored.utils.math_formulas import entropie_conditionnelle_corrigee
            from lupasco_refactored.utils.data_utils import calculer_entropie_shannon

            h_y = calculer_entropie_shannon(y_data)
            h_y_given_x = entropie_conditionnelle_corrigee(y_data, x_data)

            return h_y_given_x / h_y if h_y > 0 else 1.0

        except Exception:
            return 1.0  # Fallback conservateur

    def _perform_statistical_tests(self, x_data: List[str], y_data: List[str], mutual_info: float) -> Dict[str, Any]:
        """
        Effectue les tests statistiques de significativité (VERSION SIMPLIFIÉE)

        Args:
            x_data: Variable X
            y_data: Variable Y
            mutual_info: Information mutuelle observée

        Returns:
            dict: Résultats des tests statistiques
        """
        n_samples = len(x_data)

        # 1. Test du Chi-carré d'indépendance
        chi2_result = self._chi2_independence_test(x_data, y_data)

        # 2. Test de permutation simple
        permutation_result = self._simple_permutation_test(x_data, y_data, mutual_info, n_samples)

        # 3. Test G simplifié
        g_test_result = self._simple_g_test(x_data, y_data)

        # 4. Combinaison des p-values (moyenne pondérée simple)
        p_values = [
            chi2_result.get('p_value', 1.0),
            permutation_result.get('p_value', 1.0),
            g_test_result.get('p_value', 1.0)
        ]

        # Filtrer les p-values valides
        valid_p_values = [p for p in p_values if not np.isnan(p) and 0 <= p <= 1]

        if valid_p_values:
            # Moyenne pondérée simple (éviter scipy.stats.combine_pvalues)
            combined_p_value = np.mean(valid_p_values)
        else:
            combined_p_value = 1.0

        return {
            'chi2_test': chi2_result,
            'permutation_test': permutation_result,
            'g_test': g_test_result,
            'p_value': combined_p_value,
            'significant': combined_p_value < 0.05,
            'method': 'combined_simple'
        }

    def _chi2_independence_test(self, x_data: List[str], y_data: List[str]) -> Dict[str, Any]:
        """Test du Chi-carré d'indépendance"""
        try:
            # Construire table de contingence
            x_unique = sorted(set(x_data))
            y_unique = sorted(set(y_data))

            table = np.zeros((len(x_unique), len(y_unique)))

            for x, y in zip(x_data, y_data):
                i = x_unique.index(x)
                j = y_unique.index(y)
                table[i, j] += 1

            # Calculer chi² et p-value
            if HAS_SCIPY_STATS:
                chi2_stat, p_value, dof, expected = chi2_contingency(table)
            else:
                chi2_stat = self._manual_chi2(table)
                dof = (table.shape[0] - 1) * (table.shape[1] - 1)
                # Approximation p-value (très simplifiée)
                p_value = 1.0 if chi2_stat < dof else 0.01

            return {
                'statistic': chi2_stat,
                'p_value': p_value,
                'degrees_of_freedom': dof
            }

        except Exception as e:
            return {'statistic': 0.0, 'p_value': 1.0, 'error': str(e)}

    def _simple_permutation_test(self, x_data: List[str], y_data: List[str],
                                observed_mi: float, n_samples: int) -> Dict[str, Any]:
        """Test de permutation simple pour l'information mutuelle"""
        try:
            n_permutations = self._get_permutation_iterations(n_samples)
            mi_permutations = []

            for _ in range(min(n_permutations, 200)):  # Limiter pour performance
                # Permuter y_data
                y_permuted = np.random.permutation(y_data).tolist()

                # Calculer MI pour cette permutation
                try:
                    from lupasco_refactored.utils.math_formulas import information_mutuelle_corrigee
                    mi_perm = information_mutuelle_corrigee(x_data, y_permuted)
                    mi_permutations.append(mi_perm)
                except Exception:
                    continue

            if mi_permutations:
                # P-value = proportion de MI >= MI observée
                p_value = np.mean([mi >= observed_mi for mi in mi_permutations])
            else:
                p_value = 1.0

            return {
                'observed_mi': observed_mi,
                'p_value': p_value,
                'n_permutations': len(mi_permutations)
            }

        except Exception as e:
            return {'observed_mi': observed_mi, 'p_value': 1.0, 'error': str(e)}

    def _simple_g_test(self, x_data: List[str], y_data: List[str]) -> Dict[str, Any]:
        """Test G d'indépendance simplifié"""
        try:
            # Construire table de contingence
            x_unique = sorted(set(x_data))
            y_unique = sorted(set(y_data))

            table = np.zeros((len(x_unique), len(y_unique)))

            for x, y in zip(x_data, y_data):
                i = x_unique.index(x)
                j = y_unique.index(y)
                table[i, j] += 1

            # Calculer G statistic
            n = np.sum(table)
            row_totals = np.sum(table, axis=1)
            col_totals = np.sum(table, axis=0)

            g_stat = 0.0
            for i in range(table.shape[0]):
                for j in range(table.shape[1]):
                    observed = table[i, j]
                    if observed > 0:
                        expected = row_totals[i] * col_totals[j] / n
                        if expected > 0:
                            g_stat += 2 * observed * np.log(observed / expected)

            # Approximation p-value très simple
            dof = (table.shape[0] - 1) * (table.shape[1] - 1)
            p_value = 1.0 if g_stat < dof else 0.01

            return {
                'statistic': g_stat,
                'p_value': p_value,
                'degrees_of_freedom': dof
            }

        except Exception as e:
            return {'statistic': 0.0, 'p_value': 1.0, 'error': str(e)}
