#!/usr/bin/env python3
"""
TEST SPÉCIFIQUE POUR L'ERREUR "unsupported operand type(s) for +: 'int' and 'dict'"
===================================================================================

Ce script reproduit et teste la correction de l'erreur spécifique qui se produisait
quand la fonction fallback lupasco_entropy_analysis retournait un dictionnaire
avec une structure incompatible.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_erreur_int_dict_corrigee():
    """
    Test spécifique pour vérifier que l'erreur int + dict est corrigée
    """
    print("🔬 TEST SPÉCIFIQUE - ERREUR 'int' + 'dict' CORRIGÉE")
    print("=" * 55)
    
    try:
        # 1. TESTER LA FONCTION FALLBACK DIRECTEMENT
        print("\n📊 PHASE 1: TEST DE LA FONCTION FALLBACK")
        print("-" * 45)
        
        # Importer la fonction fallback depuis lupasco_analyzer
        from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
        
        # Créer des données de test
        test_sequences = {
            'INDEX1': ['SYNC', 'DESYNC'] * 50,
            'INDEX2': ['pair_4', 'pair_6', 'impair_5'] * 33 + ['pair_4'],
            'INDEX3': ['PLAYER', 'BANKER', 'TIE'] * 33 + ['PLAYER'],
            'INDEX5': ['SYNC_pair_4_PLAYER', 'DESYNC_pair_6_BANKER'] * 50
        }
        
        analyzer = LupascoAnalyzer(test_sequences)
        
        # Tester la méthode qui utilise lupasco_entropy_analysis
        print("   🔄 Test de analyser_lupasco_par_parties...")
        
        # Cette méthode utilise lupasco_entropy_analysis dans _analyser_partie_lupasco
        resultats = analyzer.analyser_lupasco_par_parties(nb_parties_echantillon=5)
        
        print(f"   ✅ Analyse par parties réussie : {len(resultats.get('resultats_parties', []))} parties")
        
        # 2. VÉRIFIER LA STRUCTURE DES RÉSULTATS
        print("\n🔍 PHASE 2: VÉRIFICATION DE LA STRUCTURE DES RÉSULTATS")
        print("-" * 60)
        
        if 'synthese' in resultats:
            synthese = resultats['synthese']
            print(f"   ✅ Synthèse présente : {len(synthese)} clés")
            
            # Vérifier que les moyennes sont calculables (pas d'erreur int + dict)
            if 'moyennes_metriques' in synthese:
                moyennes = synthese['moyennes_metriques']
                print(f"   ✅ Moyennes calculées : {len(moyennes)} métriques")
                
                # Afficher quelques moyennes pour vérification
                for key, value in list(moyennes.items())[:3]:
                    print(f"      {key}: {value:.6f}")
            else:
                print("   ⚠️ Pas de moyennes calculées (normal si aucune partie valide)")
        
        # 3. TESTER DIRECTEMENT LA FONCTION LUPASCO_ENTROPY_ANALYSIS FALLBACK
        print("\n🧪 PHASE 3: TEST DIRECT DE LA FONCTION FALLBACK")
        print("-" * 50)
        
        # Simuler l'absence du module formules_mathematiques_exactes
        import importlib
        import sys
        
        # Sauvegarder l'état original
        original_modules = sys.modules.copy()
        
        try:
            # Supprimer temporairement le module pour forcer l'utilisation du fallback
            if 'formules_mathematiques_exactes' in sys.modules:
                del sys.modules['formules_mathematiques_exactes']
            
            # Recharger le module lupasco_analyzer pour utiliser le fallback
            if 'lupasco_refactored.analyzers.lupasco_analyzer' in sys.modules:
                importlib.reload(sys.modules['lupasco_refactored.analyzers.lupasco_analyzer'])
            
            # Maintenant tester avec le fallback
            from lupasco_refactored.analyzers.lupasco_analyzer import LupascoAnalyzer
            
            analyzer_fallback = LupascoAnalyzer(test_sequences)
            
            # Tester une analyse qui utilise lupasco_entropy_analysis
            print("   🔄 Test avec fonction fallback...")
            resultats_fallback = analyzer_fallback.analyser_lupasco_par_parties(nb_parties_echantillon=2)
            
            print("   ✅ Fonction fallback fonctionne correctement")
            
            # Vérifier qu'il n'y a pas d'erreur int + dict
            if 'synthese' in resultats_fallback:
                synthese_fallback = resultats_fallback['synthese']
                if 'moyennes_metriques' in synthese_fallback:
                    moyennes_fallback = synthese_fallback['moyennes_metriques']
                    print(f"   ✅ Moyennes calculées avec fallback : {len(moyennes_fallback)} métriques")
                else:
                    print("   ✅ Pas d'erreur int + dict (moyennes vides mais structure correcte)")
            
        finally:
            # Restaurer l'état original des modules
            sys.modules.clear()
            sys.modules.update(original_modules)
        
        # 4. TESTER LA STRUCTURE DE RETOUR DE LA FONCTION FALLBACK
        print("\n🔧 PHASE 4: VÉRIFICATION DE LA STRUCTURE FALLBACK")
        print("-" * 55)
        
        # Importer et tester directement la fonction fallback
        try:
            # Forcer l'utilisation du fallback en simulant une ImportError
            def test_fallback_function(index1, index2, index3):
                # Copier la fonction fallback exacte
                return {
                    'entropy_index1': 0.0,
                    'entropy_index2': 0.0,
                    'entropy_index3': 0.0,
                    'entropy_joint_12': 0.0,
                    'entropy_joint_13': 0.0,
                    'entropy_joint_23': 0.0,
                    'entropy_joint_123': 0.0,
                    'entropy_cond_1_given_2': 0.0,
                    'entropy_cond_1_given_3': 0.0,
                    'entropy_cond_2_given_1': 0.0,
                    'entropy_cond_2_given_3': 0.0,
                    'entropy_cond_3_given_1': 0.0,
                    'entropy_cond_3_given_2': 0.0,
                    'mutual_info_12': 0.0,
                    'mutual_info_13': 0.0,
                    'mutual_info_23': 0.0,
                    'validation': {
                        'entropy_joint_12_check': True,
                        'entropy_joint_13_check': True,
                        'entropy_joint_23_check': True,
                        'mutual_info_12_check': True,
                        'mutual_info_13_check': True,
                        'mutual_info_23_check': True
                    },
                    'complexity_metrics': {
                        'total_entropy': 0.0,
                        'joint_entropy_all': 0.0,
                        'total_mutual_info': 0.0,
                        'interaction_information': 0.0
                    },
                    'erreur': 'Module formules_mathematiques_exactes non disponible - valeurs par défaut utilisées'
                }
            
            # Tester la fonction fallback
            result_fallback = test_fallback_function(
                test_sequences['INDEX1'],
                test_sequences['INDEX2'],
                test_sequences['INDEX3']
            )
            
            print("   ✅ Structure fallback correcte")
            
            # Tester les opérations qui causaient l'erreur
            try:
                # Simuler le calcul qui causait l'erreur
                total_entropy = (result_fallback['entropy_index1'] + 
                               result_fallback['entropy_index2'] + 
                               result_fallback['entropy_index3'])
                
                total_mutual_info = (result_fallback['mutual_info_12'] + 
                                   result_fallback['mutual_info_13'] + 
                                   result_fallback['mutual_info_23'])
                
                print(f"   ✅ Calculs arithmétiques réussis :")
                print(f"      Total entropy: {total_entropy}")
                print(f"      Total mutual info: {total_mutual_info}")
                
            except TypeError as e:
                print(f"   ❌ Erreur arithmétique détectée : {e}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur dans le test fallback : {e}")
            return False
        
        # 5. RÉSUMÉ FINAL
        print("\n🏆 PHASE 5: RÉSUMÉ FINAL")
        print("-" * 25)
        
        print("   ✅ Fonction fallback corrigée")
        print("   ✅ Structure de retour cohérente")
        print("   ✅ Opérations arithmétiques fonctionnelles")
        print("   ✅ Pas d'erreur 'int' + 'dict'")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR DANS LE TEST : {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST ERREUR 'int' + 'dict'")
    print("=" * 45)
    
    succes = test_erreur_int_dict_corrigee()
    
    print("\n" + "=" * 45)
    if succes:
        print("✅ TEST RÉUSSI - ERREUR 'int' + 'dict' CORRIGÉE")
        print("La fonction fallback retourne maintenant une structure cohérente.")
        sys.exit(0)
    else:
        print("❌ TEST ÉCHOUÉ - ERREUR 'int' + 'dict' PERSISTE")
        print("La correction n'a pas résolu le problème.")
        sys.exit(1)
