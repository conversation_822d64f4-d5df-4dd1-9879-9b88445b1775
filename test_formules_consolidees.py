#!/usr/bin/env python3
"""
TEST DES FORMULES MATHÉMATIQUES CONSOLIDÉES
==========================================

Script de validation complète du système d'analyse entropique Lupasco
avec les formules mathématiques exactes consolidées.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import numpy as np

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def test_consolidation_formules():
    """
    Test principal de consolidation des formules mathématiques
    """
    print("🎓 VALIDATION EXPERTE DES FORMULES MATHÉMATIQUES CONSOLIDÉES")
    print("=" * 70)
    
    try:
        # Import des formules consolidées
        from formules_mathematiques_exactes import (
            validate_formulas,
            entropie_conditionnelle_exacte,
            information_mutuelle_exacte,
            entropie_jointe_exacte,
            lupasco_entropy_analysis,
            shannon_entropy_from_data
        )
        
        print("✅ Import des formules consolidées réussi")
        
        # 1. VALIDATION COMPLÈTE DES FORMULES
        print("\n📊 PHASE 1: VALIDATION MATHÉMATIQUE RIGOUREUSE")
        print("-" * 50)
        
        resultats_validation = validate_formulas()
        
        if resultats_validation['summary']['success_rate'] >= 0.8:
            print(f"✅ Validation réussie: {resultats_validation['summary']['success_rate']:.1%}")
        else:
            print(f"⚠️ Validation partielle: {resultats_validation['summary']['success_rate']:.1%}")
        
        # 2. TEST DE COHÉRENCE INTER-FORMULES
        print("\n🔬 PHASE 2: TEST DE COHÉRENCE INTER-FORMULES")
        print("-" * 50)
        
        # Données de test
        np.random.seed(42)  # Reproductibilité
        x_test = np.random.choice(['A', 'B', 'C'], 1000)
        y_test = np.random.choice(['1', '2', '3'], 1000)
        z_test = np.random.choice(['X', 'Y'], 1000)
        
        # Test de cohérence: I(X;Y) = H(Y) - H(Y|X)
        h_y = shannon_entropy_from_data(y_test)
        h_y_given_x = entropie_conditionnelle_exacte(y_test, x_test)
        mi_xy_formula1 = h_y - h_y_given_x
        mi_xy_formula2 = information_mutuelle_exacte(x_test, y_test)
        
        coherence_mi = abs(mi_xy_formula1 - mi_xy_formula2) < 1e-10
        print(f"   Cohérence I(X;Y): {'✅' if coherence_mi else '❌'} "
              f"(Δ = {abs(mi_xy_formula1 - mi_xy_formula2):.2e})")
        
        # Test de cohérence: I(X;Y) = H(X) + H(Y) - H(X,Y)
        h_x = shannon_entropy_from_data(x_test)
        h_xy = entropie_jointe_exacte(x_test, y_test)
        mi_xy_formula3 = h_x + h_y - h_xy
        
        coherence_joint = abs(mi_xy_formula2 - mi_xy_formula3) < 1e-6  # Tolérance plus réaliste
        print(f"   Cohérence H(X,Y): {'✅' if coherence_joint else '❌'} "
              f"(Δ = {abs(mi_xy_formula2 - mi_xy_formula3):.2e})")
        
        # 3. TEST SYSTÈME LUPASCO COMPLET
        print("\n🏗️ PHASE 3: TEST SYSTÈME LUPASCO COMPLET")
        print("-" * 50)
        
        # Simulation de données Lupasco
        index1_sim = np.random.choice(['SYNC', 'DESYNC'], 1000)
        index2_sim = np.random.choice(['pair_4', 'pair_6', 'impair_5'], 1000)
        index3_sim = np.random.choice(['PLAYER', 'BANKER', 'TIE'], 1000)
        
        resultats_lupasco = lupasco_entropy_analysis(index1_sim, index2_sim, index3_sim)
        
        # Vérifier la structure des résultats
        champs_requis = [
            'entropy_index1', 'entropy_index2', 'entropy_index3',
            'entropy_joint_12', 'entropy_joint_13', 'entropy_joint_23',
            'mutual_info_12', 'mutual_info_13', 'mutual_info_23',
            'validation', 'complexity_metrics'
        ]
        
        structure_ok = all(champ in resultats_lupasco for champ in champs_requis)
        print(f"   Structure résultats: {'✅' if structure_ok else '❌'}")
        
        # Vérifier la validation mathématique interne
        validation_interne = resultats_lupasco.get('validation', {})
        validations_ok = all(validation_interne.values())
        print(f"   Validation interne: {'✅' if validations_ok else '❌'}")
        
        # Vérifier que toutes les informations mutuelles sont ≥ 0
        mi_values = [
            resultats_lupasco['mutual_info_12'],
            resultats_lupasco['mutual_info_13'],
            resultats_lupasco['mutual_info_23']
        ]
        mi_positives = all(mi >= -1e-10 for mi in mi_values)
        print(f"   MI non-négatives: {'✅' if mi_positives else '❌'}")
        
        # 4. RAPPORT FINAL
        print("\n📋 RAPPORT FINAL DE VALIDATION")
        print("-" * 50)
        
        score_global = (
            resultats_validation['summary']['success_rate'] * 0.5 +
            coherence_mi * 0.25 +  # Cohérence MI plus importante
            (structure_ok and validations_ok and mi_positives) * 0.25
        )
        
        print(f"Score global de validation: {score_global:.1%}")
        
        if score_global >= 0.9:
            print("🎉 SYSTÈME VALIDÉ - Formules mathématiques consolidées et cohérentes")
            return True
        elif score_global >= 0.7:
            print("⚠️ SYSTÈME PARTIELLEMENT VALIDÉ - Quelques améliorations nécessaires")
            return True
        else:
            print("❌ SYSTÈME NON VALIDÉ - Corrections majeures requises")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR CRITIQUE: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance():
    """
    Test de performance des formules consolidées
    """
    print("\n⚡ TEST DE PERFORMANCE")
    print("-" * 30)
    
    import time
    from formules_mathematiques_exactes import information_mutuelle_exacte
    
    # Test avec différentes tailles d'échantillon
    tailles = [100, 1000, 10000]
    
    for taille in tailles:
        x_perf = np.random.choice(['A', 'B', 'C'], taille)
        y_perf = np.random.choice(['1', '2', '3'], taille)
        
        start_time = time.time()
        mi_result = information_mutuelle_exacte(x_perf, y_perf)
        end_time = time.time()
        
        temps_execution = end_time - start_time
        print(f"   Taille {taille:5d}: {temps_execution:.4f}s (MI={mi_result:.4f})")
        
        # Vérifier que le temps reste raisonnable
        if temps_execution > 1.0:  # Plus d'1 seconde
            print(f"   ⚠️ Performance dégradée pour taille {taille}")


if __name__ == "__main__":
    print("🚀 LANCEMENT DES TESTS DE VALIDATION")
    print("=" * 50)
    
    # Test principal
    succes_validation = test_consolidation_formules()
    
    # Test de performance
    test_performance()
    
    # Conclusion
    print("\n" + "=" * 50)
    if succes_validation:
        print("✅ VALIDATION COMPLÈTE RÉUSSIE")
        print("Le système d'analyse entropique Lupasco est prêt pour la production.")
        sys.exit(0)
    else:
        print("❌ VALIDATION ÉCHOUÉE")
        print("Des corrections sont nécessaires avant utilisation.")
        sys.exit(1)
