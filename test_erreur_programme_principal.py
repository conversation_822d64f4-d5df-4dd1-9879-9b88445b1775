#!/usr/bin/env python3
"""
TEST POUR REPRODUIRE L'ERREUR DANS LE PROGRAMME PRINCIPAL
=========================================================

Ce script reproduit exactement l'erreur dans le contexte du programme principal
en exécutant les mêmes étapes que le programme réel.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import json
import tempfile
import traceback

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def creer_fichier_json_test():
    """
    Crée un fichier JSON de test avec des données réalistes
    """
    # Données de test réalistes
    donnees_test = {
        "INDEX1": ["SYNC", "DESYNC"] * 50,
        "INDEX2": ["pair_4", "pair_6", "impair_5"] * 33 + ["pair_4"],
        "INDEX3": ["PLAYER", "BANKER", "TIE"] * 33 + ["PLAYER"],
        "INDEX5": [],
        "INDEX2_INDEX3": [],
        "INDEX1_INDEX3": []
    }
    
    # Générer INDEX5, INDEX2_INDEX3, INDEX1_INDEX3
    for i in range(100):
        index5_val = f"{donnees_test['INDEX1'][i]}_{donnees_test['INDEX2'][i]}_{donnees_test['INDEX3'][i]}"
        donnees_test['INDEX5'].append(index5_val)
        
        index2_index3_val = f"{donnees_test['INDEX2'][i]}_{donnees_test['INDEX3'][i]}"
        donnees_test['INDEX2_INDEX3'].append(index2_index3_val)
        
        index1_index3_val = f"{donnees_test['INDEX1'][i]}_{donnees_test['INDEX3'][i]}"
        donnees_test['INDEX1_INDEX3'].append(index1_index3_val)
    
    # Ajouter des marqueurs de fin de partie
    for key in donnees_test:
        if key in ['INDEX1', 'INDEX2', 'INDEX3', 'INDEX5']:
            # Insérer des marqueurs tous les 20 éléments
            nouvelle_sequence = []
            for i, val in enumerate(donnees_test[key]):
                nouvelle_sequence.append(val)
                if (i + 1) % 20 == 0:
                    nouvelle_sequence.append("__FIN_PARTIE__")
            donnees_test[key] = nouvelle_sequence
    
    # Créer un fichier temporaire
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(donnees_test, f, indent=2)
        return f.name

def test_programme_principal():
    """
    Test du programme principal pour reproduire l'erreur
    """
    print("🔬 TEST DU PROGRAMME PRINCIPAL - REPRODUCTION ERREUR")
    print("=" * 60)
    
    fichier_json = None
    try:
        # 1. CRÉER UN FICHIER JSON DE TEST
        print("\n📊 PHASE 1: CRÉATION DU FICHIER JSON DE TEST")
        print("-" * 50)
        
        fichier_json = creer_fichier_json_test()
        print(f"   ✅ Fichier JSON créé : {fichier_json}")
        
        # 2. IMPORTER ET CRÉER L'ANALYSEUR
        print("\n📊 PHASE 2: CRÉATION DE L'ANALYSEUR")
        print("-" * 40)
        
        from analyseur import AnalyseurSequencesLupasco
        
        analyseur = AnalyseurSequencesLupasco(fichier_json)
        print(f"   ✅ Analyseur créé")
        
        # 3. CHARGER LES DONNÉES
        print("\n📊 PHASE 3: CHARGEMENT DES DONNÉES")
        print("-" * 40)
        
        analyseur.charger_donnees()
        print(f"   ✅ Données chargées")
        
        # 4. VALIDATION DES FORMULES MATHÉMATIQUES
        print("\n📊 PHASE 4: VALIDATION DES FORMULES MATHÉMATIQUES")
        print("-" * 55)
        
        try:
            analyseur.valider_formules_mathematiques()
            print(f"   ✅ Validation des formules réussie")
        except Exception as e:
            print(f"   ❌ Erreur validation formules : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS VALIDATION FORMULES !")
                traceback.print_exc()
                return False
        
        # 5. ANALYSES SPÉCIALISÉES
        print("\n📊 PHASE 5: ANALYSES SPÉCIALISÉES")
        print("-" * 35)
        
        try:
            analyseur.analyser_transitions_desync_sync()
            print(f"   ✅ Transitions DESYNC/SYNC réussies")
        except Exception as e:
            print(f"   ❌ Erreur transitions : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS TRANSITIONS !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_cycles_periode_2_et_3()
            print(f"   ✅ Cycles période 2 et 3 réussis")
        except Exception as e:
            print(f"   ❌ Erreur cycles : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS CYCLES !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_predictibilite_index3_par_index1()
            print(f"   ✅ Prédictibilité INDEX3 par INDEX1 réussie")
        except Exception as e:
            print(f"   ❌ Erreur prédictibilité INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS PRÉDICTIBILITÉ INDEX3 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_predictibilite_index5_par_index1()
            print(f"   ✅ Prédictibilité INDEX5 par INDEX1 réussie")
        except Exception as e:
            print(f"   ❌ Erreur prédictibilité INDEX5 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS PRÉDICTIBILITÉ INDEX5 !")
                traceback.print_exc()
                return False
        
        # 6. ANALYSE LUPASCO PAR PARTIES (CRITIQUE)
        print("\n📊 PHASE 6: ANALYSE LUPASCO PAR PARTIES")
        print("-" * 45)
        
        try:
            analyseur.analyser_lupasco_par_parties(nb_parties_echantillon=10)
            print(f"   ✅ Analyse Lupasco par parties réussie")
        except Exception as e:
            print(f"   ❌ Erreur Lupasco par parties : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS LUPASCO PAR PARTIES !")
                traceback.print_exc()
                return False
        
        # 7. ANALYSES INDEX5, INDEX2_INDEX3, INDEX1_INDEX3
        print("\n📊 PHASE 7: ANALYSES INDEX5, INDEX2_INDEX3, INDEX1_INDEX3")
        print("-" * 60)
        
        try:
            analyseur.analyser_index5_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX5 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX5 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX5 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_index2_index3_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX2_INDEX3 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX2_INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX2_INDEX3 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_index1_index3_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX1_INDEX3 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX1_INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX1_INDEX3 !")
                traceback.print_exc()
                return False
        
        # 8. ANALYSE COMPLÈTE
        print("\n📊 PHASE 8: ANALYSE COMPLÈTE")
        print("-" * 30)
        
        try:
            analyseur.analyser_toutes_sequences()
            print(f"   ✅ Analyse complète réussie")
        except Exception as e:
            print(f"   ❌ Erreur analyse complète : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS ANALYSE COMPLÈTE !")
                traceback.print_exc()
                return False
        
        print("\n🏆 TOUS LES TESTS RÉUSSIS - AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR GÉNÉRALE : {e}")
        if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
            print("🎯 ERREUR 'int' + 'dict' TROUVÉE !")
            traceback.print_exc()
        return False
        
    finally:
        # Nettoyer le fichier temporaire
        if fichier_json and os.path.exists(fichier_json):
            os.unlink(fichier_json)

if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DU PROGRAMME PRINCIPAL")
    print("=" * 50)
    
    succes = test_programme_principal()
    
    print("\n" + "=" * 50)
    if succes:
        print("✅ AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE DANS LE PROGRAMME PRINCIPAL")
        print("Toutes les corrections semblent fonctionner.")
        sys.exit(0)
    else:
        print("❌ ERREUR 'int' + 'dict' REPRODUITE DANS LE PROGRAMME PRINCIPAL")
        print("L'erreur a été localisée et doit être corrigée.")
        sys.exit(1)
