#!/usr/bin/env python3
"""
TEST POUR REPRODUIRE L'ERREUR DANS LE PROGRAMME PRINCIPAL
=========================================================

Ce script reproduit exactement l'erreur dans le contexte du programme principal
en exécutant les mêmes étapes que le programme réel.

Auteur : Expert Statisticien PhD
Date : 2025-06-20
"""

import sys
import os
import json
import traceback

# Ajouter le chemin du projet
sys.path.insert(0, os.path.dirname(__file__))

def verifier_fichier_dataset():
    """
    Vérifie que le fichier dataset_test_3_parties_complet.json existe et utilise l'analyseur pour le traiter
    """
    fichier_dataset = "dataset_test_3_parties_complet.json"

    if not os.path.exists(fichier_dataset):
        raise FileNotFoundError(f"Le fichier {fichier_dataset} n'existe pas dans le répertoire courant")

    # Vérifier que le fichier est valide
    try:
        with open(fichier_dataset, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Vérifier la structure du dataset (format parties de baccarat)
        if 'parties' not in data:
            raise ValueError("Structure de dataset invalide : clé 'parties' manquante")

        parties = data['parties']
        if not parties:
            raise ValueError("Aucune partie trouvée dans le dataset")

        # Compter le nombre total de mains
        total_mains = 0
        for partie in parties:
            if 'mains' in partie:
                total_mains += len(partie['mains'])

        print(f"   ✅ Fichier dataset valide : {fichier_dataset}")
        print(f"   📊 Nombre de parties : {len(parties)}")
        print(f"   📊 Total de mains : {total_mains}")

        return fichier_dataset

    except json.JSONDecodeError as e:
        raise ValueError(f"Fichier JSON invalide : {e}")
    except Exception as e:
        raise ValueError(f"Erreur lors de la lecture du dataset : {e}")

def test_programme_principal():
    """
    Test du programme principal pour reproduire l'erreur
    """
    print("🔬 TEST DU PROGRAMME PRINCIPAL - REPRODUCTION ERREUR")
    print("=" * 60)
    
    fichier_json = None
    try:
        # 1. VÉRIFIER LE FICHIER DATASET
        print("\n📊 PHASE 1: VÉRIFICATION DU FICHIER DATASET")
        print("-" * 50)

        fichier_json = verifier_fichier_dataset()
        print(f"   ✅ Fichier dataset vérifié : {fichier_json}")
        
        # 2. IMPORTER ET CRÉER L'ANALYSEUR
        print("\n📊 PHASE 2: CRÉATION DE L'ANALYSEUR")
        print("-" * 40)
        
        from analyseur import AnalyseurSequencesLupasco
        
        analyseur = AnalyseurSequencesLupasco(fichier_json)
        print(f"   ✅ Analyseur créé")
        
        # 3. CHARGER LES DONNÉES
        print("\n📊 PHASE 3: CHARGEMENT DES DONNÉES")
        print("-" * 40)
        
        analyseur.charger_donnees()
        print(f"   ✅ Données chargées")
        
        # 4. VALIDATION DES FORMULES MATHÉMATIQUES
        print("\n📊 PHASE 4: VALIDATION DES FORMULES MATHÉMATIQUES")
        print("-" * 55)
        
        try:
            analyseur.valider_formules_mathematiques()
            print(f"   ✅ Validation des formules réussie")
        except Exception as e:
            print(f"   ❌ Erreur validation formules : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS VALIDATION FORMULES !")
                traceback.print_exc()
                return False
        
        # 5. ANALYSES SPÉCIALISÉES
        print("\n📊 PHASE 5: ANALYSES SPÉCIALISÉES")
        print("-" * 35)
        
        try:
            analyseur.analyser_transitions_desync_sync()
            print(f"   ✅ Transitions DESYNC/SYNC réussies")
        except Exception as e:
            print(f"   ❌ Erreur transitions : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS TRANSITIONS !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_cycles_periode_2_et_3()
            print(f"   ✅ Cycles période 2 et 3 réussis")
        except Exception as e:
            print(f"   ❌ Erreur cycles : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS CYCLES !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_predictibilite_index3_par_index1()
            print(f"   ✅ Prédictibilité INDEX3 par INDEX1 réussie")
        except Exception as e:
            print(f"   ❌ Erreur prédictibilité INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS PRÉDICTIBILITÉ INDEX3 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_predictibilite_index5_par_index1()
            print(f"   ✅ Prédictibilité INDEX5 par INDEX1 réussie")
        except Exception as e:
            print(f"   ❌ Erreur prédictibilité INDEX5 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS PRÉDICTIBILITÉ INDEX5 !")
                traceback.print_exc()
                return False
        
        # 6. ANALYSE LUPASCO PAR PARTIES (CRITIQUE)
        print("\n📊 PHASE 6: ANALYSE LUPASCO PAR PARTIES")
        print("-" * 45)
        
        try:
            analyseur.analyser_lupasco_par_parties(nb_parties_echantillon=10)
            print(f"   ✅ Analyse Lupasco par parties réussie")
        except Exception as e:
            print(f"   ❌ Erreur Lupasco par parties : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS LUPASCO PAR PARTIES !")
                traceback.print_exc()
                return False
        
        # 7. ANALYSES INDEX5, INDEX2_INDEX3, INDEX1_INDEX3
        print("\n📊 PHASE 7: ANALYSES INDEX5, INDEX2_INDEX3, INDEX1_INDEX3")
        print("-" * 60)
        
        try:
            analyseur.analyser_index5_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX5 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX5 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX5 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_index2_index3_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX2_INDEX3 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX2_INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX2_INDEX3 !")
                traceback.print_exc()
                return False
        
        try:
            analyseur.analyser_index1_index3_avec_formules_exactes()
            print(f"   ✅ Analyse INDEX1_INDEX3 réussie")
        except Exception as e:
            print(f"   ❌ Erreur INDEX1_INDEX3 : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS INDEX1_INDEX3 !")
                traceback.print_exc()
                return False
        
        # 8. ANALYSE COMPLÈTE
        print("\n📊 PHASE 8: ANALYSE COMPLÈTE")
        print("-" * 30)
        
        try:
            analyseur.analyser_toutes_sequences()
            print(f"   ✅ Analyse complète réussie")
        except Exception as e:
            print(f"   ❌ Erreur analyse complète : {e}")
            if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
                print("   🎯 ERREUR 'int' + 'dict' TROUVÉE DANS ANALYSE COMPLÈTE !")
                traceback.print_exc()
                return False
        
        print("\n🏆 TOUS LES TESTS RÉUSSIS - AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE")
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR GÉNÉRALE : {e}")
        if "unsupported operand type(s) for +: 'int' and 'dict'" in str(e):
            print("🎯 ERREUR 'int' + 'dict' TROUVÉE !")
            traceback.print_exc()
        return False
        
    finally:
        # Pas de nettoyage nécessaire pour le fichier dataset existant
        pass

if __name__ == "__main__":
    print("🚀 LANCEMENT DU TEST DU PROGRAMME PRINCIPAL")
    print("=" * 50)
    
    succes = test_programme_principal()
    
    print("\n" + "=" * 50)
    if succes:
        print("✅ AUCUNE ERREUR 'int' + 'dict' DÉTECTÉE DANS LE PROGRAMME PRINCIPAL")
        print("Toutes les corrections semblent fonctionner.")
        sys.exit(0)
    else:
        print("❌ ERREUR 'int' + 'dict' REPRODUITE DANS LE PROGRAMME PRINCIPAL")
        print("L'erreur a été localisée et doit être corrigée.")
        sys.exit(1)
